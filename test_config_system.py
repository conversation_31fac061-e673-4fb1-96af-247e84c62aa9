#!/usr/bin/env python3
"""Test script for the configuration system."""

import os
from utils import get_chat_model
from config.globalconfig import get_or_create_settings_ins


def test_config_loading():
    """Test configuration loading."""
    print("🔧 Testing configuration loading...")
    
    config = get_or_create_settings_ins()
    print(f"App name: {config.app.name}")
    print(f"Default model: {config.models.default}")
    print(f"Available models: {list(config.models.model_fields.keys())}")
    print(f"Video base dir: {config.paths.video_base_dir}")
    print(f"Screenshot base dir: {config.paths.screenshot_base_dir}")
    print()


def test_model_loading():
    """Test model loading."""
    print("🤖 Testing model loading...")
    
    # Test available models
    models_to_test = [
        "default",
        "fix",
        "vision",
        "seed",
        "ui_tars"
    ]
    
    for model_name in models_to_test:
        try:
            print(f"Loading {model_name}...")
            model = get_chat_model(model_name)
            print(f"✅ {model_name}: {type(model).__name__}")
        except Exception as e:
            print(f"❌ {model_name}: {str(e)}")
    print()


def test_env_vars():
    """Test environment variables."""
    print("🌍 Testing environment variables...")
    
    api_keys = {
        "Azure OpenAI": os.getenv("AZURE_OPENAI_API_KEY"),
        "Doubao AI": os.getenv("DOUBAO_API_KEY"),
    }
    
    for name, key in api_keys.items():
        status = "✅ Set" if key else "❌ Not set"
        print(f"{name}: {status}")
    
    if not any(api_keys.values()):
        print("\n⚠️  No API keys found. Please:")
        print("1. Copy .env.example to .env")
        print("2. Fill in your AZURE_OPENAI_API_KEY and DOUBAO_API_KEY in .env file")
    print()


def test_chat_functionality():
    """Test basic chat functionality."""
    print("💬 Testing chat functionality...")
    
    try:
        model = get_chat_model()
        response = model.invoke("Hello! Please respond with 'Configuration test successful!'")
        print(f"✅ Chat test successful: {response.content}")
    except Exception as e:
        print(f"❌ Chat test failed: {str(e)}")
        print("Make sure you have set up API keys in .env file")
    print()


def main():
    """Main test function."""
    print("=" * 60)
    print("🚄 High-Speed Train Configuration System Test")
    print("=" * 60)
    
    test_config_loading()
    test_env_vars()
    test_model_loading()
    test_chat_functionality()
    
    print("=" * 60)
    print("✅ Configuration system test completed!")
    print("=" * 60)


if __name__ == "__main__":
    main()
