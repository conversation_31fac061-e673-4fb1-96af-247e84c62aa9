# 配置系统说明

这个配置系统基于click-pilot项目的配置架构，支持从`.env`文件和TOML配置文件加载配置。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制示例环境文件并填入您的API密钥：

```bash
cp .env.example .env
```

编辑`.env`文件：

```bash
# 运行环境
HIGH_SPEED_TRAIN_ENV=no_prod

# API密钥
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
DOUBAO_API_KEY=506a6605-1c08-4694-ab53-d559a761cfde
```

### 3. 使用get_chat_model

```python
from utils import get_chat_model

# 使用默认模型
model = get_chat_model()

# 使用指定模型
model = get_chat_model("fix")
model = get_chat_model("vision")
model = get_chat_model("seed")
model = get_chat_model("ui_tars")

# 发送消息
response = model.invoke("你好，请介绍一下高铁的工作原理")
print(response.content)
```

## 📁 目录结构

```
config/
├── __init__.py          # 空的初始化文件
├── globalconfig.py      # 全局配置类定义
├── no_prod.toml        # 开发环境配置
└── README.md           # 本文档

utils/
├── __init__.py         # 工具模块入口
├── config_utils.py     # 配置工具函数
├── model_utils.py      # 模型工具函数（包含get_chat_model）
└── toml_config.py      # TOML配置源

.env.example            # 环境变量示例文件
test_config_system.py   # 配置系统测试脚本
```

## 🎯 支持的模型

| 模型名称 | 配置键 | 提供商 | 说明 |
|---------|--------|--------|------|
| GPT-5 Chat | `default`/`fix` | Azure OpenAI | 默认和修复模型 |
| GPT-4o | `gpt4o` | Azure OpenAI | GPT-4o模型 |
| GPT-4.1 | `gpt41` | Azure OpenAI | GPT-4.1模型 |
| Doubao Vision | `vision` | 豆包AI | 视觉模型 |
| Doubao Seed | `seed` | 豆包AI | 思维链模型 |
| Doubao UI-TARS | `ui_tars` | 豆包AI | UI操作模型 |

## 🔧 配置文件说明

### TOML配置文件

配置文件位于`config/no_prod.toml`，包含：

- **app**: 应用基础配置
- **models**: 模型名称映射
- **paths**: 路径配置
- **llms**: 各个LLM的详细配置

### 环境变量替换

TOML文件中可以使用`${VAR_NAME}`格式引用环境变量：

```toml
[llms.gpt5]
provider = "azure_openai"
model = "gpt-5-chat"
external_args = { azure_endpoint = "https://east-us2-quwan-yw-infra-02.openai.azure.com", azure_deployment = "gpt-5-chat", api_version = "2025-04-01-preview", api_key = "${AZURE_OPENAI_API_KEY}" }
```

## 🧪 测试配置

运行测试脚本验证配置：

```bash
python test_config_system.py
```

测试内容包括：
- 配置加载测试
- 环境变量检查
- 模型加载测试
- 基础聊天功能测试

## 🔄 扩展配置

### 添加新模型

1. 在`config/globalconfig.py`的`ModelSettings`中添加新字段
2. 在`config/no_prod.toml`中添加对应的LLM配置
3. 设置相应的环境变量

### 自定义配置

可以通过修改TOML文件来调整模型参数：

```toml
[llms.custom_model]
provider = "openai"
temperature = 0.5
model = "custom-model-name"
external_args = { 
    api_key = "${CUSTOM_API_KEY}",
    max_tokens = 2048,
    custom_param = "value"
}
```

## 🛠️ 故障排除

### 常见问题

1. **模块导入错误**
   ```
   ModuleNotFoundError: No module named 'utils'
   ```
   确保在项目根目录运行脚本

2. **API密钥未设置**
   ```
   Error: API key not found
   ```
   检查`.env`文件是否存在且包含正确的AZURE_OPENAI_API_KEY

3. **TOML文件解析错误**
   ```
   Error loading TOML file
   ```
   检查TOML文件语法是否正确

### 调试模式

设置环境变量启用调试：

```bash
export HIGH_SPEED_TRAIN_ENV=no_prod
```

这将加载`config/no_prod.toml`配置文件。
