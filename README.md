# 🚄 高铁巡检帧提取器

专为高铁巡检项目设计，**每秒保留最高清且稳定的10张图片**。

## ✨ 核心特性

- **🎯 精确提取**: 每秒严格保留10帧最高质量图片
- **🔍 多维质量评估**: 清晰度+稳定性+曝光+对比度综合评分
- **⏱️ 完整覆盖**: 每秒都有帧，确保时间连续性
- **🚫 异常过滤**: 自动识别并排除异常帧（全黑、全白、失真）
- **📊 详细报告**: 生成完整的提取统计和质量分析

## 🚀 快速使用

### 1. 安装依赖
```bash
pip install opencv-python numpy
```

### 2. 运行提取
```bash
python frame_extractor.py
```

就这么简单！程序会自动：
- 分析视频整体质量
- 按秒提取最佳10帧
- 保存高质量图片
- 生成详细报告

## 🧠 质量评估算法

### 综合评分体系
每帧都会进行7维质量评估：

| 评估维度 | 权重 | 说明 |
|---------|------|------|
| **清晰度** | 30% | 拉普拉斯方差，检测图像锐度 |
| **边缘清晰度** | 25% | Sobel算子，检测设备轮廓 |
| **梯度幅值** | 20% | 整体细节丰富程度 |
| **运动模糊** | 10% | 检测晃动造成的模糊 |
| **曝光稳定性** | 10% | 避免过曝或欠曝 |
| **对比度** | 5% | 确保图像层次分明 |

### 异常检测
自动排除以下异常帧：
- 全黑帧（亮度 < 10）
- 全白帧（亮度 > 245）
- 低对比度帧（标准差 < 5）
- 严重模糊帧（清晰度 < 10）

## 📊 输出结果

### 目录结构
```
assets/
└── frames_20241231_143022/
    ├── frames/                      # 提取的帧
    │   ├── frame_000001_s0_0.12_q1250.jpg
    │   ├── frame_000002_s0_0.45_q1180.jpg
    │   └── ...
    └── extraction_report.json      # 详细报告
```

### 文件命名规则
`frame_序号_s秒数_小数秒_q质量分数.jpg`

例如：`frame_000001_s0_0.12_q1250.jpg`
- 序号：000001
- 时间：第0秒的0.12秒
- 质量分数：1250

## 📈 提取策略

### 两阶段处理
1. **预分析阶段**：快速扫描视频，确定质量阈值
2. **精确提取阶段**：按秒处理，每秒选择最佳10帧

### 每秒处理流程
1. 收集该秒内所有达标帧
2. 按综合质量评分排序
3. 选择评分最高的10帧
4. 按时间顺序重新排列

### 质量保证
- **最低质量线**：只保留超过30%分位数的帧
- **时间均匀性**：确保每秒都有帧覆盖
- **稳定性优先**：优选稳定清晰的帧

## 📋 提取报告

生成的JSON报告包含：

```json
{
  "extraction_info": {
    "total_frames_extracted": 850,
    "target_fps": 10,
    "actual_avg_fps": 9.8,
    "extraction_percentage": 33.3
  },
  "frames_per_second_stats": {
    "min_frames_per_second": 8,
    "max_frames_per_second": 10,
    "avg_frames_per_second": 9.8,
    "seconds_with_10_frames": 75
  },
  "quality_summary": {
    "avg_quality_score": 1180.5,
    "min_quality_score": 850.2,
    "max_quality_score": 1650.8
  }
}
```

## 🎯 适用场景

### 高铁巡检项目
- ✅ 设备状态检查
- ✅ 缺陷识别分析
- ✅ 安全隐患排查
- ✅ 维护记录存档

### 技术优势
- **存储优化**：相比原30fps减少67%存储空间
- **质量保证**：每帧都经过严格质量检验
- **处理友好**：固定10fps便于后续算法处理
- **时间精确**：保持完整的时间覆盖

## 💡 使用建议

1. **首次使用**：直接运行查看效果和报告
2. **批量处理**：可以修改video_path处理多个视频
3. **质量调优**：根据报告中的质量分布调整阈值
4. **后续处理**：提取的帧可直接用于AI模型训练

## 🔧 自定义调整

如需调整提取策略，可修改以下参数：

```python
# 在 analyze_video_quality() 中
'quality_threshold': np.percentile(quality_scores, 30)  # 调整质量阈值

# 在 extract_frames_per_second() 中  
frames_to_select = min(10, len(frames_in_second))  # 调整每秒帧数

# 在 calculate_quality_score() 中调整权重
sharpness * 0.3 +           # 清晰度权重
edge_strength * 0.25 +      # 边缘权重
# ...
```

---

**🎯 专业品质，精准提取，为高铁巡检而生！**
