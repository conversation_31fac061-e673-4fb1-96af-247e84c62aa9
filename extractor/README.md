# 视频清晰帧提取器

从晃动的视频中自动提取清晰度较高的帧。

## 功能特点

- **多种清晰度评估算法**：支持拉普拉斯方差、梯度幅值、Brenner梯度、Sobel方差、Tenengrad等方法
- **灵活的帧选择策略**：支持按百分比、阈值或固定数量选择清晰帧
- **可视化分析**：生成清晰度分析图表
- **详细报告**：输出JSON格式的分析报告
- **高效处理**：支持采样间隔设置，提高处理速度

## 安装依赖

```bash
pip install opencv-python numpy matplotlib
```

## 使用方法

### 基本用法

```bash
python video_frame_extractor.py your_video.mp4
```

### 高级用法

```bash
# 选择清晰度最高的5%帧
python video_frame_extractor.py video.mp4 --selection-method top_percent --threshold 0.05

# 选择清晰度最高的20帧
python video_frame_extractor.py video.mp4 --selection-method top_n --threshold 20

# 使用梯度方法评估清晰度，每3帧采样一次
python video_frame_extractor.py video.mp4 --method gradient --sample-interval 3

# 自定义输出目录
python video_frame_extractor.py video.mp4 --output-dir my_clear_frames
```

### 参数说明

- `video_path`: 输入视频文件路径
- `--output-dir`: 输出目录（默认：clear_frames）
- `--method`: 清晰度评估方法
  - `laplacian`: 拉普拉斯方差（推荐，默认）
  - `gradient`: 梯度幅值
  - `brenner`: Brenner梯度
  - `sobel`: Sobel方差
  - `tenengrad`: Tenengrad算法
- `--selection-method`: 帧选择方法
  - `top_percent`: 选择清晰度前N%的帧（默认）
  - `threshold`: 选择清晰度超过阈值的帧
  - `top_n`: 选择清晰度最高的N帧
- `--threshold`: 选择阈值
  - top_percent: 0.1 = 10%
  - threshold: 标准差的倍数
  - top_n: 帧的数量
- `--sample-interval`: 采样间隔（1=每帧分析，2=每2帧分析一次）
- `--no-plot`: 不显示分析图表

## 输出文件

程序会在输出目录中生成：

1. **清晰帧图片**：格式为 `clear_frame_XXXX_tYY.YYs_sZZ.ZZ.jpg`
   - XXXX: 帧序号
   - YY.YY: 时间戳（秒）
   - ZZ.ZZ: 清晰度分数

2. **分析图表**：`sharpness_analysis.png` - 显示整个视频的清晰度变化

3. **分析报告**：`analysis_report.json` - 包含详细的分析数据

## 清晰度评估方法说明

### 拉普拉斯方差（推荐）
- 计算图像的拉普拉斯算子方差
- 值越大表示边缘越清晰
- 对大多数场景效果最好

### 梯度幅值
- 计算图像梯度的平均值
- 适合检测整体锐度

### Brenner梯度
- 专门用于评估图像清晰度
- 对水平边缘敏感

### Sobel方差
- 基于Sobel算子的方差计算
- 平衡水平和垂直边缘

### Tenengrad
- 基于Sobel算子的平方和
- 对高频细节敏感

## 使用建议

1. **对于一般晃动视频**：使用默认的拉普拉斯方差方法
2. **处理大视频文件**：增加采样间隔（如 `--sample-interval 3`）
3. **需要少量高质量帧**：使用 `top_n` 方法
4. **需要固定比例的帧**：使用 `top_percent` 方法
5. **视频质量差异大**：使用 `threshold` 方法

## 示例

```python
from extractor.video_frame_extractor import VideoFrameExtractor

# 创建提取器
extractor = VideoFrameExtractor("shaky_video.mp4", "output_frames")

# 分析帧清晰度
frames_data = extractor.extract_frames_with_sharpness(method='laplacian')

# 选择清晰度最高的10%帧
clear_frames = extractor.select_clear_frames(frames_data, 'top_percent', 0.1)

# 保存帧
extractor.save_frames(clear_frames)

# 生成分析图表
extractor.plot_sharpness_analysis(frames_data, clear_frames)
```
