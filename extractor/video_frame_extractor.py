#!/usr/bin/env python3
"""
视频清晰帧提取器
从晃动的视频中提取清晰度较高的帧
"""

import cv2
import numpy as np
import os
import argparse
import matplotlib.pyplot as plt
from pathlib import Path
from typing import List, Tuple, Dict
import json


class FrameSharpnessAnalyzer:
    """帧清晰度分析器"""
    
    def __init__(self):
        self.methods = {
            'laplacian': self._laplacian_variance,
            'gradient': self._gradient_magnitude,
            'brenner': self._brenner_gradient,
            'sobel': self._sobel_variance,
            'tenengrad': self._tenengrad
        }
    
    def _laplacian_variance(self, image: np.ndarray) -> float:
        """拉普拉斯方差法 - 最常用的清晰度评估方法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        return laplacian.var()
    
    def _gradient_magnitude(self, image: np.ndarray) -> float:
        """梯度幅值法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        return magnitude.mean()
    
    def _brenner_gradient(self, image: np.ndarray) -> float:
        """Brenner梯度法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        brenner = np.abs(gray[2:, :] - gray[:-2, :])
        return brenner.mean()
    
    def _sobel_variance(self, image: np.ndarray) -> float:
        """Sobel方差法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        sobel = np.sqrt(sobel_x**2 + sobel_y**2)
        return sobel.var()
    
    def _tenengrad(self, image: np.ndarray) -> float:
        """Tenengrad算法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        tenengrad = sobel_x**2 + sobel_y**2
        return tenengrad.mean()
    
    def calculate_sharpness(self, image: np.ndarray, method: str = 'laplacian') -> float:
        """计算图像清晰度"""
        if method not in self.methods:
            raise ValueError(f"不支持的方法: {method}. 支持的方法: {list(self.methods.keys())}")
        return self.methods[method](image)


class VideoFrameExtractor:
    """视频帧提取器"""
    
    def __init__(self, video_path: str, output_dir: str = "clear_frames"):
        self.video_path = video_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.analyzer = FrameSharpnessAnalyzer()
        
        # 视频信息
        self.cap = cv2.VideoCapture(video_path)
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.duration = self.total_frames / self.fps
        
        print(f"视频信息:")
        print(f"  路径: {video_path}")
        print(f"  帧率: {self.fps:.2f} FPS")
        print(f"  总帧数: {self.total_frames}")
        print(f"  时长: {self.duration:.2f} 秒")
    
    def extract_frames_with_sharpness(self, method: str = 'laplacian', 
                                    sample_interval: int = 1) -> List[Dict]:
        """提取帧并计算清晰度"""
        frames_data = []
        frame_count = 0
        
        print(f"开始分析帧清晰度 (方法: {method}, 采样间隔: {sample_interval})...")
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            if frame_count % sample_interval == 0:
                # 计算清晰度
                sharpness = self.analyzer.calculate_sharpness(frame, method)
                timestamp = frame_count / self.fps
                
                frames_data.append({
                    'frame_number': frame_count,
                    'timestamp': timestamp,
                    'sharpness': sharpness,
                    'frame': frame.copy()
                })
                
                if len(frames_data) % 100 == 0:
                    print(f"已处理 {len(frames_data)} 帧...")
            
            frame_count += 1
        
        self.cap.release()
        print(f"完成! 共分析了 {len(frames_data)} 帧")
        return frames_data
    
    def select_clear_frames(self, frames_data: List[Dict], 
                          selection_method: str = 'top_percent',
                          threshold: float = 0.1) -> List[Dict]:
        """选择清晰帧"""
        sharpness_values = [frame['sharpness'] for frame in frames_data]
        
        if selection_method == 'top_percent':
            # 选择清晰度前N%的帧
            num_frames = max(1, int(len(frames_data) * threshold))
            sorted_frames = sorted(frames_data, key=lambda x: x['sharpness'], reverse=True)
            selected_frames = sorted_frames[:num_frames]
            
        elif selection_method == 'threshold':
            # 选择清晰度超过阈值的帧
            mean_sharpness = np.mean(sharpness_values)
            std_sharpness = np.std(sharpness_values)
            threshold_value = mean_sharpness + threshold * std_sharpness
            selected_frames = [frame for frame in frames_data 
                             if frame['sharpness'] > threshold_value]
            
        elif selection_method == 'top_n':
            # 选择清晰度最高的N帧
            num_frames = int(threshold)
            sorted_frames = sorted(frames_data, key=lambda x: x['sharpness'], reverse=True)
            selected_frames = sorted_frames[:num_frames]
            
        else:
            raise ValueError(f"不支持的选择方法: {selection_method}")
        
        print(f"选择方法: {selection_method}")
        print(f"从 {len(frames_data)} 帧中选择了 {len(selected_frames)} 帧")
        return selected_frames
    
    def save_frames(self, frames: List[Dict], prefix: str = "clear_frame") -> None:
        """保存选中的帧"""
        print(f"保存 {len(frames)} 帧到 {self.output_dir}...")
        
        # 按时间戳排序
        frames_sorted = sorted(frames, key=lambda x: x['timestamp'])
        
        for i, frame_data in enumerate(frames_sorted):
            filename = f"{prefix}_{i+1:04d}_t{frame_data['timestamp']:.2f}s_s{frame_data['sharpness']:.2f}.jpg"
            filepath = self.output_dir / filename
            cv2.imwrite(str(filepath), frame_data['frame'])
        
        print(f"帧已保存到: {self.output_dir}")
    
    def plot_sharpness_analysis(self, frames_data: List[Dict], 
                              selected_frames: List[Dict] = None) -> None:
        """绘制清晰度分析图"""
        timestamps = [frame['timestamp'] for frame in frames_data]
        sharpness_values = [frame['sharpness'] for frame in frames_data]
        
        plt.figure(figsize=(12, 6))
        plt.plot(timestamps, sharpness_values, 'b-', alpha=0.7, label='所有帧')
        
        if selected_frames:
            selected_timestamps = [frame['timestamp'] for frame in selected_frames]
            selected_sharpness = [frame['sharpness'] for frame in selected_frames]
            plt.scatter(selected_timestamps, selected_sharpness, 
                       c='red', s=50, label='选中的清晰帧', zorder=5)
        
        plt.xlabel('时间 (秒)')
        plt.ylabel('清晰度分数')
        plt.title('视频帧清晰度分析')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 保存图表
        plot_path = self.output_dir / "sharpness_analysis.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"分析图表已保存到: {plot_path}")
    
    def save_analysis_report(self, frames_data: List[Dict], 
                           selected_frames: List[Dict], method: str) -> None:
        """保存分析报告"""
        sharpness_values = [frame['sharpness'] for frame in frames_data]
        
        report = {
            'video_info': {
                'path': self.video_path,
                'fps': self.fps,
                'total_frames': self.total_frames,
                'duration': self.duration
            },
            'analysis_info': {
                'method': method,
                'total_analyzed_frames': len(frames_data),
                'selected_frames': len(selected_frames)
            },
            'statistics': {
                'mean_sharpness': float(np.mean(sharpness_values)),
                'std_sharpness': float(np.std(sharpness_values)),
                'min_sharpness': float(np.min(sharpness_values)),
                'max_sharpness': float(np.max(sharpness_values))
            },
            'selected_frames_info': [
                {
                    'frame_number': frame['frame_number'],
                    'timestamp': frame['timestamp'],
                    'sharpness': frame['sharpness']
                }
                for frame in selected_frames
            ]
        }
        
        report_path = self.output_dir / "analysis_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"分析报告已保存到: {report_path}")


def main():
    parser = argparse.ArgumentParser(description='从晃动视频中提取清晰帧')
    parser.add_argument('video_path', help='输入视频路径')
    parser.add_argument('--output-dir', default='clear_frames', help='输出目录')
    parser.add_argument('--method', default='laplacian', 
                       choices=['laplacian', 'gradient', 'brenner', 'sobel', 'tenengrad'],
                       help='清晰度评估方法')
    parser.add_argument('--selection-method', default='top_percent',
                       choices=['top_percent', 'threshold', 'top_n'],
                       help='帧选择方法')
    parser.add_argument('--threshold', type=float, default=0.1,
                       help='选择阈值 (top_percent: 0.1=10%, threshold: 标准差倍数, top_n: 帧数)')
    parser.add_argument('--sample-interval', type=int, default=1,
                       help='采样间隔 (1=每帧都分析, 2=每2帧分析一次)')
    parser.add_argument('--no-plot', action='store_true', help='不显示分析图表')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.video_path):
        print(f"错误: 视频文件不存在: {args.video_path}")
        return
    
    # 创建提取器
    extractor = VideoFrameExtractor(args.video_path, args.output_dir)
    
    # 提取帧并分析清晰度
    frames_data = extractor.extract_frames_with_sharpness(
        method=args.method, 
        sample_interval=args.sample_interval
    )
    
    # 选择清晰帧
    selected_frames = extractor.select_clear_frames(
        frames_data, 
        selection_method=args.selection_method,
        threshold=args.threshold
    )
    
    # 保存帧
    extractor.save_frames(selected_frames)
    
    # 保存分析报告
    extractor.save_analysis_report(frames_data, selected_frames, args.method)
    
    # 绘制分析图表
    if not args.no_plot:
        extractor.plot_sharpness_analysis(frames_data, selected_frames)


if __name__ == "__main__":
    main()
