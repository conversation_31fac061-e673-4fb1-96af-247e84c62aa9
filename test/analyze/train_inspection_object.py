import base64
import os
from typing import List

system_prompt = """
##### 角色 #####
- 你是一位资深的高铁巡检员；你现在的任务是通过视频流的连续帧来识别出<巡检目标列表>的目标；

##### 巡检目标列表 #####
- 巡检目标：高铁车厢显示屏
    - 描述：安装在高铁车厢侧面外部，用于动态显示车次、到站信息、车厢编号等内容（车厢号借此呈现）。
    - 参考 <高铁车厢显示屏图> 进行识别
- 巡检目标：高铁车头雨刮器
    - 描述：安装在高铁车头的雨刮器，用于清理车头的雨刮器。
    - 参考 <高铁车头雨刮器图> 进行识别
    
- 巡检目标：高铁边门滑槽
    - 描述：安装于车厢侧门边缘的长条形金属槽，用于引导边门滑动。
    - 参考 <高铁边门滑槽图1>、<高铁边门滑槽图2> 进行识别
    
- 巡检目标：高铁受电弓
    - 描述：安装在高铁顶部，形状为弓形，用于从接触网获取电能的装置。
    - 参考 <高铁受电弓图(升起状态)>、<高铁受电弓图(降落状态)> 进行识别
    
- 巡检目标：高铁车头灯罩
    - 描述：安装在高铁车头前部，保护车头前部的灯具。
    - 参考 <高铁车头灯罩图> 进行识别
    
- 巡检目标：高铁重联车钩
    - 描述：安装在高铁车头前部，通常位于车头与车头之间，裸露在外的金属部件，用于连接两组列车的装置。
    - 参考 <高铁重联车钩图> 进行识别
    
- 巡检目标：高铁风挡
    - 描述：安装在高铁车厢之间，用于连接两节车厢的拼接，是柔性密封型外壳。
    - 参考 <高铁风挡图1>、<高铁风挡图2> 进行识别

    

##### 输出格式 #####
- 如果未识别到 <任务目标物体>， 输出空json列表即可: []
- 如果是清晰识别到（不模糊），则输出以下json 格式
[
    {
        "name": "frame_000001_t1.1s",
        "description": "目标的简短描述",
        "target": ["目标1", "目标2", ..."],
        "confidence": "置信度，取值0-1，越接近1越好; 只保留 >=0.8 为高置信度，高清晰度"
        "grounding_bbox": [[xmin, ymin, xmax, ymax], [xmin, ymin, xmax, ymax], ...]
    }
]

##### 巡检工作流程 #####
1. 分析用户给出的每一帧的内容，记住其特征及名称；
2. 逐一比对每一帧的内容，判断是否为<巡检目标列表>中的目标；
3. 由于给出的是连续帧，如果在单一帧中无法看清目标，需要结合多帧来判断；
4. 找出目标后，严格遵循<输出格式>进行内容输出，直到每一帧都判断完毕；

"""

image_display_screen_prompt = """##### 高铁车厢显示屏图 ######"""
image_wiper_prompt = """##### 高铁车头雨刮器图 ######"""
image_sliding_slot_1_prompt = """##### 高铁边门滑槽图1 ######"""
image_sliding_slot_2_prompt = """##### 高铁边门滑槽图2 ######"""
image_pantograph_raised_prompt = """##### 高铁受电弓图(升起状态) ######"""
image_pantograph_lowered_prompt = """##### 高铁受电弓图(降落状态) ######"""
image_headlight_shield_prompt = """##### 高铁车头灯罩图 ######"""
image_coupler_prompt = """##### 高铁重联车钩图 ######"""
image_wind_shield_1_prompt = """##### 高铁风挡图1 ######"""
image_wind_shield_2_prompt = """##### 高铁风挡图2 ######"""


user_start_prompt = """开始巡检任务， 请根据 <巡检工作流程> 开始你的巡检"""

display_screen_image = "test/analyze/mark_inspection_object_img/display_screen_marked.jpg"
wiper_image = "test/analyze/mark_inspection_object_img/wiper_marked.jpg"
sliding_slot_image_1 = "test/analyze/mark_inspection_object_img/door_slide_channel_2_marked.jpg"
sliding_slot_image_2 = "test/analyze/mark_inspection_object_img/door_slide_channel_3_marked.jpg"
pantograph_raised_image = "test/analyze/mark_inspection_object_img/pantograph_raised_marked.jpg"
pantograph_lowered_image = "test/analyze/mark_inspection_object_img/pantograph_lowered_marked.jpg"
headlight_shield_image_1 = "test/analyze/mark_inspection_object_img/train_light_2_marked.jpg"
headlight_shield_image_2 = "test/analyze/mark_inspection_object_img/train_light_3_marked.jpg"
coupler_image = "test/analyze/mark_inspection_object_img/coupler_marked.jpg"
wind_shield_image_1 = "test/analyze/mark_inspection_object_img/wind_shield_2_marked.jpg"
wind_shield_image_2 = "test/analyze/mark_inspection_object_img/wind_shield_3_marked.jpg"

def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64格式"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_frame_id(image_path: str) -> str:
    """从图片路径中提取frame ID，如 frame_00103"""
    filename = os.path.basename(image_path)
    # 提取frame_XXXXXX部分
    if filename.startswith("frame_"):
        frame_id = filename.split("_")[0] + "_" + filename.split("_")[1] + "_" + filename.split("_")[2]
        return frame_id
    return filename


def create_batch_message(image_paths: List[str]) -> dict:
    """为一批图片创建vision模型消息，每张图片都有对应的文本说明"""
    # 构建消息内容，先添加总体提示


    content = [
        {
            "type": "text",
            "text": "好，现在开始执行你的<巡检任务>"
        },
        {
            "type": "text",
            "text": f"我将发送{len(image_paths)}张图片，每张图片前都会标注其名称："
        }
    ]

    # 为每张图片添加：文本说明 + 图片
    for image_path in image_paths:
        frame_id = extract_frame_id(image_path)

        # 添加图片名称说明
        content.append({
            "type": "text",
            "text": f"以下图片名称为{frame_id}："
        })

        # 添加对应的图片
        try:
            base64_image = encode_image_to_base64(image_path)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            })
        except Exception as e:
            print(f"警告：无法编码图片 {image_path}: {str(e)}")
            # 如果图片编码失败，添加错误说明
            content.append({
                "type": "text",
                "text": f"（图片{frame_id}编码失败）"
            })

    return {"role": "user", "content": content}


def analyze_inspection_object():
    messages = []
    system_message = {
        "role": "system",
        "content": system_prompt
    }
    reference_image_messages = [
        {
            "role": "user",
            "content":  [
                {
                    "type": "text",
                    "text": image_display_screen_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(display_screen_image)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_wiper_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(wiper_image)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_sliding_slot_1_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(sliding_slot_image_1)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_sliding_slot_2_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(sliding_slot_image_2)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_pantograph_raised_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(pantograph_raised_image)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_pantograph_lowered_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(pantograph_lowered_image)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_headlight_shield_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(headlight_shield_image_1)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_coupler_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(coupler_image)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_wind_shield_1_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(wind_shield_image_1)}"
                    }
                },
                {
                    "type": "text",
                    "text": image_wind_shield_2_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{encode_image_to_base64(wind_shield_image_2)}"
                    }
                }
            ]
        }
    ]
    messages.append(system_message)
    messages.extend(reference_image_messages)
    # 待补充