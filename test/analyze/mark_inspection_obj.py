
import base64
import sys
import cv2
import os
import numpy as np
from pathlib import Path
from typing import Tuple
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils import get_chat_model


def get_font(font_size=20):
    """获取字体对象"""
    try:
        # macOS/Linux/Windows 中文字体路径
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/simhei.ttf",  # Windows 黑体
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, font_size)

        return ImageFont.load_default()
    except Exception:
        return ImageFont.load_default()


def get_text_size(text, font_size=20):
    """
    获取文字的实际尺寸

    Args:
        text: 要测量的文字
        font_size: 字体大小

    Returns:
        (width, height): 文字的宽度和高度
    """
    font = get_font(font_size)

    # 创建临时图像来测量文字尺寸
    temp_img = Image.new('RGB', (1, 1))
    draw = ImageDraw.Draw(temp_img)

    # 获取文字边界框
    bbox = draw.textbbox((0, 0), text, font=font)
    width = bbox[2] - bbox[0]
    height = bbox[3] - bbox[1]

    return (width, height)


def draw_chinese_text(img, text, position, font_size=20, color=(0, 0, 0)):
    """
    在OpenCV图像上绘制中文文字

    Args:
        img: OpenCV图像 (BGR格式)
        text: 要绘制的文字
        position: 文字位置 (x, y)
        font_size: 字体大小
        color: 文字颜色 (B, G, R)

    Returns:
        绘制文字后的图像
    """
    # 转换BGR到RGB
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(img_rgb)

    # 创建绘制对象
    draw = ImageDraw.Draw(pil_img)
    font = get_font(font_size)

    # 绘制文字 (PIL使用RGB格式)
    color_rgb = (color[2], color[1], color[0])  # BGR转RGB
    draw.text(position, text, font=font, fill=color_rgb)

    # 转换回BGR格式
    img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    return img_bgr


def doubao_bbox_to_pixels(doubao_bbox: tuple[int, int, int, int],
                         image_width: int, image_height: int) -> tuple[int, int, int, int]:
    """
    将豆包视觉模型的bbox坐标转换为像素坐标
    豆包使用1000倍放大的归一化坐标，即范围 [0, 1000]

    Args:
        doubao_bbox: 豆包坐标 (x_min, y_min, x_max, y_max)，范围 [0, 1000]
        image_width: 图片宽度
        image_height: 图片高度

    Returns:
        像素坐标 (xmin, ymin, xmax, ymax)
    """
    x_min, y_min, x_max, y_max = doubao_bbox

    # 豆包坐标转换为像素坐标
    xmin = int(x_min * image_width / 1000)
    ymin = int(y_min * image_height / 1000)
    xmax = int(x_max * image_width / 1000)
    ymax = int(y_max * image_height / 1000)

    # 确保坐标在有效范围内
    xmin = max(0, min(xmin, image_width - 1))
    ymin = max(0, min(ymin, image_height - 1))
    xmax = max(0, min(xmax, image_width - 1))
    ymax = max(0, min(ymax, image_height - 1))

    # 确保 xmax > xmin, ymax > ymin
    if xmax <= xmin:
        xmax = xmin + 1
    if ymax <= ymin:
        ymax = ymin + 1

    return (xmin, ymin, xmax, ymax)



def analyze_inspection_object(image_path: str, object_name: str) -> bool:
    """通过LLM视觉模型识别图片内容"""

    # 读取并编码图片
    with open(image_path, "rb") as image_file:
        base64_image = base64.b64encode(image_file.read()).decode('utf-8')

    # 获取视觉模型
    vision_model = get_chat_model("vision")

    # 构建消息
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"请分析这张图片中是否包含该目标：'{object_name}'，并简单描述目标的位置以及特征，以及通过 bbox 坐标圈住目标(坐标格式为一个tuple)。"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpg;base64,{base64_image}"
                    }
                }
            ]
        }
    ]

    # 发送请求
    response = vision_model.invoke(messages)
    print(response.content)

    return True

def mark_inspection_object(
        image_path: str,
        bbox: Tuple[int, int, int, int],
        output_dir: str = "test/analyze/mark_inspection_object_img",
        label: str = None,
        show_image: bool = False
) -> bool:
    """
    在图片上标记巡检对象的边界框

    Args:
        image_path: 输入图片路径
        bbox: 边界框坐标 (x_min, y_min, x_max, y_max)，豆包坐标范围 [0,1000]，将自动转换为像素坐标
        output_dir: 输出目录
        label: 可选标签文字，如果提供则显示在bbox上方
        show_image: 是否显示图片

    Returns:
        bool: 处理是否成功
    """
    try:
        # 检查图片是否存在
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return False

        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False

        # 获取图片尺寸
        height, width, channels = img.shape
        print(f"📏 图片尺寸: {width}×{height}×{channels}")

        # 解析bbox坐标（豆包 -> 像素）
        xmin, ymin, xmax, ymax = doubao_bbox_to_pixels(bbox, width, height)
        print(f"📍 豆包坐标: {bbox} -> 像素坐标: ({xmin}, {ymin}, {xmax}, {ymax})")

        # 验证和修正bbox坐标
        original_bbox = (xmin, ymin, xmax, ymax)
        xmin = max(0, min(xmin, width - 1))
        ymin = max(0, min(ymin, height - 1))
        xmax = max(0, min(xmax, width - 1))
        ymax = max(0, min(ymax, height - 1))

        # 确保bbox有效
        if xmax <= xmin:
            xmax = xmin + 1
        if ymax <= ymin:
            ymax = ymin + 1

        if original_bbox != (xmin, ymin, xmax, ymax):
            print(f"⚠️ bbox坐标已修正: {original_bbox} -> ({xmin}, {ymin}, {xmax}, {ymax})")

        # 计算bbox的宽度和高度
        bbox_width = xmax - xmin
        bbox_height = ymax - ymin
        print(f"📐 bbox尺寸: {bbox_width}×{bbox_height}")
        print(f"📍 标记坐标: ({xmin}, {ymin}, {xmax}, {ymax})")

        # 创建图片副本用于标记
        marked_img = img.copy()

        # 绘制绿色矩形框（线宽 2px）
        cv2.rectangle(marked_img, (xmin, ymin), (xmax, ymax), (0, 255, 0), 2)

        # 如果提供了标签，则显示
        if label:
            # 获取文字的实际尺寸
            font_size = 14
            text_width, text_height = get_text_size(label, font_size)

            # 计算标签背景位置
            label_bg_y = max(ymin - 10, text_height + 5)

            # 绘制标签背景（增加一些边距）
            padding = 5
            cv2.rectangle(marked_img, (xmin, label_bg_y - text_height - padding),
                         (xmin + text_width + padding * 2, label_bg_y + padding), (0, 255, 0), -1)

            # 绘制中文标签文字
            marked_img = draw_chinese_text(marked_img, label, (xmin + padding, label_bg_y - text_height),
                                         font_size=font_size, color=(0, 0, 0))

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 生成输出文件名
        input_filename = Path(image_path).stem
        output_filename = f"{input_filename}_marked.jpg"
        output_path = os.path.join(output_dir, output_filename)

        # 保存标记后的图片
        success = cv2.imwrite(output_path, marked_img)
        if success:
            print(f"✅ 标记后的图片已保存到: {output_path}")
        else:
            print(f"❌ 保存图片失败: {output_path}")
            return False

        # 显示图片
        if show_image:
            window_name = "巡检对象标记 - 按任意键关闭"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)

            # 如果图片太大，调整窗口大小
            if width > 1200 or height > 800:
                scale = min(1200/width, 800/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                cv2.resizeWindow(window_name, new_width, new_height)

            cv2.imshow(window_name, marked_img)
            print("🖼️ 图片已显示，按任意键关闭...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        print("✅ 标记处理完成!")
        return True

    except Exception as e:
        print(f"❌ 标记处理失败: {str(e)}")
        return False


if __name__ == "__main__":
    image_path = "test/analyze/inspection_object_img/wiper.jpg"
    object_name = "高铁边门滑槽"
    # analyze_inspection_object(image_path, object_name)
    mark_inspection_object(image_path,  (273, 231, 336, 820), label=object_name)