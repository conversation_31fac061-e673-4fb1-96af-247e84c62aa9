#!/usr/bin/env python3
"""
火车显示屏bbox标记脚本 - 修正版
用于标记和验证火车车厢显示屏的边界框
"""

import cv2
import os
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw, ImageFont


def doubao_bbox_to_pixels(doubao_bbox: tuple[int, int, int, int],
                         image_width: int, image_height: int) -> tuple[int, int, int, int]:
    """
    将豆包视觉模型的bbox坐标转换为像素坐标
    豆包使用1000倍放大的归一化坐标，即范围 [0, 1000]

    Args:
        doubao_bbox: 豆包坐标 (x_min, y_min, x_max, y_max)，范围 [0, 1000]
        image_width: 图片宽度
        image_height: 图片高度

    Returns:
        像素坐标 (xmin, ymin, xmax, ymax)
    """
    x_min, y_min, x_max, y_max = doubao_bbox

    # 豆包坐标转换为像素坐标
    xmin = int(x_min * image_width / 1000)
    ymin = int(y_min * image_height / 1000)
    xmax = int(x_max * image_width / 1000)
    ymax = int(y_max * image_height / 1000)

    # 确保坐标在有效范围内
    xmin = max(0, min(xmin, image_width - 1))
    ymin = max(0, min(ymin, image_height - 1))
    xmax = max(0, min(xmax, image_width - 1))
    ymax = max(0, min(ymax, image_height - 1))

    # 确保 xmax > xmin, ymax > ymin
    if xmax <= xmin:
        xmax = xmin + 1
    if ymax <= ymin:
        ymax = ymin + 1

    return (xmin, ymin, xmax, ymax)





def pixels_to_doubao_bbox(pixel_bbox: tuple[int, int, int, int],
                         image_width: int, image_height: int) -> tuple[int, int, int, int]:
    """
    将像素坐标转换为豆包bbox坐标

    Args:
        pixel_bbox: 像素坐标 (xmin, ymin, xmax, ymax)
        image_width: 图片宽度
        image_height: 图片高度

    Returns:
        豆包坐标 (x_min, y_min, x_max, y_max)，范围 [0, 1000]
    """
    xmin, ymin, xmax, ymax = pixel_bbox

    # 转换为豆包坐标
    x_min = int(xmin * 1000 / image_width)
    y_min = int(ymin * 1000 / image_height)
    x_max = int(xmax * 1000 / image_width)
    y_max = int(ymax * 1000 / image_height)

    # 确保在 [0, 1000] 范围内
    x_min = max(0, min(1000, x_min))
    y_min = max(0, min(1000, y_min))
    x_max = max(0, min(1000, x_max))
    y_max = max(0, min(1000, y_max))

    return (x_min, y_min, x_max, y_max)


def get_font(font_size=20):
    """获取字体对象"""
    try:
        # macOS/Linux/Windows 中文字体路径
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "C:/Windows/Fonts/msyh.ttc",  # Windows 微软雅黑
            "C:/Windows/Fonts/simhei.ttf",  # Windows 黑体
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, font_size)

        return ImageFont.load_default()
    except Exception:
        return ImageFont.load_default()


def get_text_size(text, font_size=20):
    """
    获取文字的实际尺寸

    Args:
        text: 要测量的文字
        font_size: 字体大小

    Returns:
        (width, height): 文字的宽度和高度
    """
    font = get_font(font_size)

    # 创建临时图像来测量文字尺寸
    temp_img = Image.new('RGB', (1, 1))
    draw = ImageDraw.Draw(temp_img)

    # 获取文字边界框
    bbox = draw.textbbox((0, 0), text, font=font)
    width = bbox[2] - bbox[0]
    height = bbox[3] - bbox[1]

    return (width, height)


def draw_chinese_text(img, text, position, font_size=20, color=(0, 0, 0)):
    """
    在OpenCV图像上绘制中文文字

    Args:
        img: OpenCV图像 (BGR格式)
        text: 要绘制的文字
        position: 文字位置 (x, y)
        font_size: 字体大小
        color: 文字颜色 (B, G, R)

    Returns:
        绘制文字后的图像
    """
    # 转换BGR到RGB
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    pil_img = Image.fromarray(img_rgb)

    # 创建绘制对象
    draw = ImageDraw.Draw(pil_img)
    font = get_font(font_size)

    # 绘制文字 (PIL使用RGB格式)
    color_rgb = (color[2], color[1], color[0])  # BGR转RGB
    draw.text(position, text, font=font, fill=color_rgb)

    # 转换回BGR格式
    img_bgr = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    return img_bgr


def mark_train_display_bbox(image_path: str, bbox: tuple | None = None,
                           output_path: str | None = None,
                           show_image: bool = True, label: str | None = None) -> bool:
    """
    在火车显示屏图片上标记bbox（仅支持豆包坐标）

    Args:
        image_path: 输入图片路径
        bbox: 边界框坐标（豆包坐标），格式 (x_min, y_min, x_max, y_max)，范围 [0,1000]
        output_path: 输出图片路径，如果为None则不保存
        show_image: 是否显示图片
        label: 可选标签文字，如果提供则显示在bbox上方

    Returns:
        bool: 处理是否成功
    """
    try:
        # 检查图片是否存在
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return False

        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False

        # 获取图片尺寸
        height, width, channels = img.shape
        print(f"📏 图片尺寸: {width}×{height}×{channels}")

        # 处理bbox坐标（仅支持豆包格式）
        if bbox is None:
            # 默认的火车显示屏bbox（豆包坐标，经验值，位于底部）
            default_doubao = (150, 800, 940, 990)
            xmin, ymin, xmax, ymax = doubao_bbox_to_pixels(default_doubao, width, height)
            print("📍 使用默认豆包bbox坐标")
        else:
            # 豆包坐标转换为像素坐标 [0,1000]
            print(f"📍 输入豆包坐标: {bbox}")
            xmin, ymin, xmax, ymax = doubao_bbox_to_pixels(bbox, width, height)
            print(f"📍 转换为像素坐标: ({xmin}, {ymin}, {xmax}, {ymax})")

        # 验证和修正bbox坐标
        original_bbox = (xmin, ymin, xmax, ymax)
        xmin = max(0, min(xmin, width - 1))
        ymin = max(0, min(ymin, height - 1))
        xmax = max(0, min(xmax, width - 1))
        ymax = max(0, min(ymax, height - 1))

        # 确保bbox有效
        if xmax <= xmin:
            xmax = xmin + 1
        if ymax <= ymin:
            ymax = ymin + 1

        if original_bbox != (xmin, ymin, xmax, ymax):
            print(f"⚠️ bbox坐标已修正: {original_bbox} -> ({xmin}, {ymin}, {xmax}, {ymax})")

        # 计算bbox的宽度和高度
        bbox_width = xmax - xmin
        bbox_height = ymax - ymin
        print(f"📐 bbox尺寸: {bbox_width}×{bbox_height}")

        # 创建图片副本用于标记
        marked_img = img.copy()

        # 绘制绿色矩形框（线宽 1px）
        cv2.rectangle(marked_img, (xmin, ymin), (xmax, ymax), (0, 255, 0), 2)

        # 如果提供了标签，则显示
        if label:
            # 获取文字的实际尺寸
            font_size = 20
            text_width, text_height = get_text_size(label, font_size)

            # 计算标签背景位置
            label_bg_y = max(ymin - 10, text_height + 5)

            # 绘制标签背景（增加一些边距）
            padding = 5
            cv2.rectangle(marked_img, (xmin, label_bg_y - text_height - padding),
                         (xmin + text_width + padding * 2, label_bg_y + padding), (0, 255, 0), -1)

            # 绘制中文标签文字
            marked_img = draw_chinese_text(marked_img, label, (xmin + padding, label_bg_y - text_height),
                                         font_size=font_size, color=(0, 0, 0))





        # 保存标记后的图片
        if output_path:
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            success = cv2.imwrite(output_path, marked_img)
            if success:
                print(f"✅ 标记后的图片已保存到: {output_path}")
            else:
                print(f"❌ 保存图片失败: {output_path}")
                return False

        # 显示图片
        if show_image:
            window_name = "火车显示屏 BBox 标记 - 按任意键关闭"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)

            # 如果图片太大，调整窗口大小
            if width > 1200 or height > 800:
                scale = min(1200/width, 800/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                cv2.resizeWindow(window_name, new_width, new_height)

            cv2.imshow(window_name, marked_img)
            print("🖼️ 图片已显示，按任意键关闭...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        print("✅ 处理完成!")
        return True

    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        return False


def mark_model_detection_results(image_path: str, detection_results: list,
                                output_path: str = None, show_image: bool = True,
                                show_labels: bool = False) -> bool:
    """
    标记模型检测结果（支持多个bbox，可选显示标签）

    Args:
        image_path: 输入图片路径
        detection_results: 检测结果列表，每个元素包含:
                          {
                              "bbox": [x_min, y_min, x_max, y_max],  # 豆包坐标 [0,1000]
                              "label": "显示屏",  # 可选，仅在show_labels=True时使用
                              "confidence": 0.95  # 可选，仅在show_labels=True时使用
                          }
        output_path: 输出图片路径
        show_image: 是否显示图片
        show_labels: 是否显示标签和置信度

    Returns:
        bool: 处理是否成功
    """
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False

        height, width, channels = img.shape
        print(f"📏 图片尺寸: {width}×{height}×{channels}")
        print(f"🔍 检测到 {len(detection_results)} 个目标")

        # 创建图片副本
        marked_img = img.copy()

        # 定义颜色列表
        colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋红色
            (0, 255, 255),  # 黄色
        ]

        for i, result in enumerate(detection_results):
            # 解析检测结果（豆包坐标）
            bbox_norm = result.get("bbox", [0, 0, 1000, 1000])
            label = result.get("label", "Object") if show_labels else None
            confidence = result.get("confidence", 0.0) if show_labels else None

            # 转换为像素坐标（豆包 -> 像素）
            xmin, ymin, xmax, ymax = doubao_bbox_to_pixels(bbox_norm, width, height)

            # 选择颜色
            color = colors[i % len(colors)]

            # 绘制矩形框
            cv2.rectangle(marked_img, (xmin, ymin), (xmax, ymax), color, 2)



            # 如果需要显示标签
            if show_labels and label:
                # 创建标签文字
                if confidence is not None:
                    label_text = f"{label} {confidence:.2f}"
                else:
                    label_text = label

                # 获取文字的实际尺寸
                font_size = 16
                text_width, text_height = get_text_size(label_text, font_size)

                # 计算标签背景位置
                label_bg_y = max(ymin - 10, text_height + 5)

                # 绘制标签背景（增加一些边距）
                padding = 5
                cv2.rectangle(marked_img, (xmin, label_bg_y - text_height - padding),
                             (xmin + text_width + padding * 2, label_bg_y + padding), color, -1)

                # 绘制中文标签文字
                marked_img = draw_chinese_text(marked_img, label_text, (xmin + padding, label_bg_y - text_height),
                                             font_size=font_size, color=(255, 255, 255))

            print(f"  {i+1}. bbox像素坐标: ({xmin},{ymin},{xmax},{ymax})")

        # 保存图片
        if output_path:
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            success = cv2.imwrite(output_path, marked_img)
            if success:
                print(f"✅ 标记后的图片已保存到: {output_path}")
            else:
                print(f"❌ 保存图片失败: {output_path}")
                return False

        # 显示图片
        if show_image:
            window_name = f"模型检测结果 ({len(detection_results)}个目标) - 按任意键关闭"
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)

            if width > 1200 or height > 800:
                scale = min(1200/width, 800/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                cv2.resizeWindow(window_name, new_width, new_height)

            cv2.imshow(window_name, marked_img)
            print("🖼️ 图片已显示，按任意键关闭...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        print("✅ 模型检测结果标记完成!")
        return True

    except Exception as e:
        print(f"❌ 标记模型检测结果失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 配置参数
    # image_path = "test/analyze/display_screen.jpg"
    image_path = "test/analyze/1.jpg"
    output_path = "test/analyze/marked_display_screen.jpg"

    print("🚄 火车显示屏bbox标记工具")
    print("=" * 50)

    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        print("请确保图片文件存在，或修改 image_path 变量")
        exit(1)

    # 使用默认bbox进行标记
    success = mark_train_display_bbox(
        bbox= (136, 500, 447, 902) ,
        image_path=image_path,
        output_path=output_path,
        show_image=False,
        label="你好"
    )

    if success:
        print(f"\n🎉 成功完成bbox标记!")
        print(f"📁 输出文件: {output_path}")
    else:
        print(f"\n❌ bbox标记失败!")

    # 可选：使用自定义bbox
    print(f"\n💡 提示: 如需使用自定义bbox，可以修改代码中的bbox参数")
    print(f"   例如: bbox=(100, 350, 750, 470)")

    print("\n✅ 脚本执行完成!")