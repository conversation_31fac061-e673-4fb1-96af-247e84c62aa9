
#!/usr/bin/env python3
"""
火车车厢分析模块
通过批量发送图片给vision模型来分析火车车厢数量
"""

import base64
import json
import os
import sys
import glob
from pathlib import Path
from pprint import pprint
from typing import List, Tuple, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from utils import get_chat_model


def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64格式"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def get_frame_images(frames_dir: str) -> List[str]:
    """获取指定目录下的所有frame图片文件，按文件名排序"""
    if not os.path.exists(frames_dir):
        raise FileNotFoundError(f"目录不存在: {frames_dir}")

    # 获取所有jpg图片文件
    pattern = os.path.join(frames_dir, "frame_*.jpg")
    image_files = glob.glob(pattern)

    # 按文件名排序
    image_files.sort()

    print(f"找到 {len(image_files)} 张图片")
    return image_files


def extract_frame_id(image_path: str) -> str:
    """从图片路径中提取frame ID，如 frame_00103"""
    filename = os.path.basename(image_path)
    # 提取frame_XXXXXX部分
    if filename.startswith("frame_"):
        frame_id = filename.split("_")[0] + "_" + filename.split("_")[1] + "_" + filename.split("_")[2]
        return frame_id
    return filename


def merge_result_data(results: List[Dict[str, str]]) -> List[Dict[str, str]]:
    """
    根据车厢号分组，保留每个车厢号中confidence评分最高的记录

    Args:
        results: 包含车厢分析结果的字典列表

    Returns:
        去重后的结果列表，每个车厢号只保留最高评分的记录
    """
    if not results:
        return []

    # 按车厢号分组
    carriage_groups = {}

    for result in results:
        carriage_number = result.get('carriage_number')
        confidence = float(result.get('confidence', 0))

        # 跳过没有车厢号的记录
        if not carriage_number:
            continue

        # 如果这个车厢号还没有记录，或者当前记录的confidence更高
        if (carriage_number not in carriage_groups or
            confidence > carriage_groups[carriage_number]['confidence']):
            carriage_groups[carriage_number] = result

    # 转换为列表并按车厢号排序
    merged_results = list(carriage_groups.values())
    merged_results.sort(key=lambda x: x.get('carriage_number', ''))

    return merged_results
    

def create_batch_message(image_paths: List[str]) -> dict:
    """为一批图片创建vision模型消息，每张图片都有对应的文本说明"""
    # 构建消息内容，先添加总体提示


    content = [
        {
            "type": "text",
            "text": "好，现在开始执行你的<巡检任务>"
        },
        {
            "type": "text",
            "text": f"我将发送{len(image_paths)}张图片，每张图片前都会标注其名称："
        }
    ]

    # 为每张图片添加：文本说明 + 图片
    for image_path in image_paths:
        frame_id = extract_frame_id(image_path)

        # 添加图片名称说明
        content.append({
            "type": "text",
            "text": f"以下图片名称为{frame_id}："
        })

        # 添加对应的图片
        try:
            base64_image = encode_image_to_base64(image_path)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
            })
        except Exception as e:
            print(f"警告：无法编码图片 {image_path}: {str(e)}")
            # 如果图片编码失败，添加错误说明
            content.append({
                "type": "text",
                "text": f"（图片{frame_id}编码失败）"
            })

    return {"role": "user", "content": content}


def train_carriage_analysis(
    frames_dir: str = "assets/extracted_frames_20250904_170940",
    batch_size: int = 30,
    system_prompt: str = "请分析帮我分析我给出的高铁图片，。"
) -> List[Dict[str, str]]:
    """
    通过图片分析火车车厢数量

    Args:
        frames_dir: 图片目录路径
        batch_size: 每批发送的图片数量，默认20张
        prompt: 发送给vision模型的提示词
    """
    print("🚄 开始火车车厢分析")
    print("=" * 60)

    results = []

    try:
        # 获取vision模型y
        vision_model = get_chat_model(model_name="vision")
        print(f"✅ Vision模型加载成功: {type(vision_model).__name__}")

        # 获取所有图片文件
        image_files = get_frame_images(frames_dir)

        if not image_files:
            print("❌ 未找到任何图片文件")
            return results

        # 按批次处理图片
        total_batches = (len(image_files) + batch_size - 1) // batch_size
        print(f"📊 将分 {total_batches} 批处理，每批 {batch_size} 张图片")
        print("-" * 60)

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(image_files))
            batch_images = image_files[start_idx:end_idx]

            print(f"\n🔍 处理第 {batch_idx + 1}/{total_batches} 批")
            print(f"📷 图片范围: {extract_frame_id(batch_images[0])} 到 {extract_frame_id(batch_images[-1])}")
            print(f"📊 图片数量: {len(batch_images)}")

            try:
                # system prompt
                system_message = {
                        "role": "system",
                        "content": system_prompt
                }

                base64_image_display_screen = encode_image_to_base64("test/analyze/display_screen.jpg")
                # display screen
                display_screen_prompt = {
                    "role": "system",
                    "content":  [
                        {
                            "type": "text",
                            "text": "##### 车厢显示屏图片 #####"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image_display_screen}"
                            }
                        }
                    ]
                }

                # 创建消息
                message = create_batch_message(batch_images)

                # 发送给vision模型
                print("🤖 正在分析...")
                response = vision_model.invoke([system_message, display_screen_prompt, message])

                print("✅ 分析完成!")
                print("\n📝 分析结果:")
                print("=" * 60)
                print(response.content)
                print("=" * 60)
                results.extend(json.loads(response.content))

            except Exception as e:
                print(f"❌ 第 {batch_idx + 1} 批处理失败: {str(e)}")
                raise e

        print(f"\n🎉 火车车厢分析完成!")

    except Exception as e:
        print(f"❌ 分析过程出错: {str(e)}")

    finally:
        results = merge_result_data(results)
        return results


if __name__ == "__main__":
    # 示例用法
    custom_prompt = """
##### 角色 #####
- 你是为专业高铁和火车巡检员，你现在通过连续的视频帧巡检当前的高铁车厢；
- 关注当前高铁车厢的情况，通过连续的视频帧，整体理解需要巡检的内容

##### 巡检任务 #####
- 请根据用户给定连续的视频帧，根据<识别规则>识别给定的 <任务目标物体> 中的‘车厢显示屏’；

##### 任务目标物体 #####
**车厢显示屏**
    - 描述：安装在高铁车厢侧面外部，用于动态显示车次、到站信息、车厢编号等内容（车厢号借此呈现）。
    - 参照 <车厢显示屏图片> 进行识别


##### 识别规则 #####
- 清晰识别到'车厢显示屏'，清晰看到车厢号，不要有任何推测

##### 图片说明 #####
- 图片名称中的frame_XXXXXX_Xs 表示图片的代号

##### 输出格式 #####
- 如果未识别到 <任务目标物体>， 输出空json列表即可: []
- 如果是清晰识别到（不模糊），则输出以下json 格式
[
    {
        "name": "frame_000001_t1.1s",
        "description": "如果是车厢可见的显示屏，描述其内容；如果不是车厢显示屏，输出空字符串''",
        "carriage_number": "车厢号",
        "confidence": "置信度，取值0-1，越接近1越好; 只保留 >=0.8 为高置信度，高清晰度"
        "grounding_bbox": "[xmin, ymin, xmax, ymax]"
    }
]



"""

    # 如需运行实际分析，取消下面的注释
    data = train_carriage_analysis(
        frames_dir="assets/extracted_frames_20250904_170940",
        batch_size=30,
        system_prompt=custom_prompt
    )
    pprint(data)
