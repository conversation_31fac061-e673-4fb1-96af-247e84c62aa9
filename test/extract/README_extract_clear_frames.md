# 🚄 高铁站台巡检视频帧提取工具

## 📖 概述

`extract_clear_frames.py` 是一个专门用于从抖动视频中提取最清晰帧的Python脚本。该工具特别针对高铁站台巡检视频进行了优化，能够智能识别和保留高质量的关键帧，为后续的AI模型识别提供清晰的图像数据。

## 🎯 主要功能

- **智能质量评估**: 综合考虑清晰度、运动幅度、曝光质量和稳定性
- **运动检测**: 使用光流算法区分有意义的相机运动和无意义的抖动
- **稳定性分析**: 通过RANSAC算法识别稳定像素，优先选择运动一致的帧
- **黑白帧过滤**: 自动识别并过滤纯黑色和纯白色的无效帧
- **时间分组**: 按秒分组，确保时间覆盖的均匀性
- **详细日志**: 实时显示处理进度和统计信息

## 🚀 快速开始

### 基本使用（推荐）

```bash
# 使用默认配置处理巡检视频
python test/extract_clear_frames.py
```

这将：
- 处理 `assets/video/inspection.mp4`
- 每秒提取8帧最佳图像
- 输出到 `assets/extracted_frames_YYYYMMDD_HHMMSS/`

### 自定义输入输出

```bash
python test/extract_clear_frames.py \
    --input path/to/your/video.mp4 \
    --output path/to/output/directory
```

## ⚙️ 参数详解

### 基础参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--input` | `assets/video/inspection.mp4` | 输入视频文件路径 |
| `--output` | 自动生成 | 输出目录（自动创建带时间戳的目录） |
| `--max-per-second` | `8` | 每秒最多保留的帧数 |

### 质量控制参数

| 参数 | 默认值 | 说明 | 调整建议 |
|------|--------|------|----------|
| `--alpha` | `0.7` | 运动惩罚强度 | 降低值保留更多运动帧，提高值更严格过滤 |
| `--downscale` | `1` | 光流计算降采样因子 | 480p视频建议保持1，高分辨率可用2-4 |
| `--no-exposure` | 关闭 | 禁用曝光权重 | 光照稳定时可启用以提升速度 |

### 稳定性分析参数

| 参数 | 默认值 | 说明 | 调整建议 |
|------|--------|------|----------|
| `--use-stable` | `True` | 启用稳定性分析 | 巡检视频建议启用 |
| `--beta` | `0.6` | 稳定性权重指数 | 0.4-0.8适合巡检场景 |
| `--grid-step` | `12` | 稳定性分析网格间距 | 480p用12，高分辨率用16-32 |
| `--model` | `affine` | 运动模型 | `affine`适合一般运动，`homography`适合透视变化 |
| `--resid-thr` | `2.5` | RANSAC重投影阈值 | 降低值更严格，提高值更宽松 |
| `--min-grad` | `6.0` | 最小梯度阈值 | 低纹理场景可降低到3-5 |
| `--stable-downscale` | `1` | 稳定性分析降采样 | 高分辨率视频可用2-4加速 |

### 黑白帧过滤参数

| 参数 | 默认值 | 说明 | 调整建议 |
|------|--------|------|----------|
| `--no-filter-black-white` | 关闭 | 禁用黑白帧过滤 | 一般建议保持启用 |
| `--black-threshold` | `15` | 黑帧像素阈值 | 0-15像素值被认为是黑色 |
| `--white-threshold` | `240` | 白帧像素阈值 | 240-255像素值被认为是白色 |
| `--extreme-pixel-ratio` | `0.85` | 极值像素比例阈值 | 85%以上极值像素认为是无效帧 |

## 📊 使用场景与参数配置

### 1. 标准巡检视频（推荐配置）

```bash
python test/extract_clear_frames.py \
    --max-per-second 8 \
    --alpha 0.7 \
    --use-stable \
    --beta 0.6
```

**适用于**: 480p 30fps的常规巡检视频，平衡质量和覆盖度

### 2. 详细检查模式（高质量）

```bash
python test/extract_clear_frames.py \
    --max-per-second 6 \
    --alpha 0.5 \
    --beta 0.4 \
    --downscale 1
```

**适用于**: 需要更多细节的重点区域检查

### 3. 快速处理模式（高效率）

```bash
python test/extract_clear_frames.py \
    --max-per-second 5 \
    --alpha 1.0 \
    --downscale 2 \
    --stable-downscale 2
```

**适用于**: 大量视频的批量处理

### 4. 低光照环境

```bash
python test/extract_clear_frames.py \
    --max-per-second 6 \
    --alpha 0.8 \
    --min-grad 4.0 \
    --resid-thr 3.0
```

**适用于**: 夜间或光照不足的巡检视频

### 5. 高分辨率视频（4K/1080p）

```bash
python test/extract_clear_frames.py \
    --downscale 3 \
    --grid-step 24 \
    --stable-downscale 3 \
    --max-per-second 6
```

**适用于**: 高分辨率视频的快速处理

## 📁 输出文件说明

### 文件命名格式

```
frame_001234_t41.23s_S1456_sh234_fl0.156_sr0.823.jpg
```

- `frame_001234`: 原视频中的帧索引
- `t41.23s`: 时间戳（第41.23秒）
- `S1456`: 综合质量分数
- `sh234`: 清晰度分数（拉普拉斯方差）
- `fl0.156`: 光流幅度（运动程度）
- `sr0.823`: 稳定性比例（82.3%的像素稳定）

### 质量指标解读

| 指标 | 好的范围 | 说明 |
|------|----------|------|
| 质量分数(S) | > 500 | 综合评分，越高越好 |
| 清晰度(sh) | > 100 | 图像清晰度，480p下的合理阈值 |
| 光流(fl) | < 0.5 | 运动幅度，越小越稳定 |
| 稳定性(sr) | > 0.6 | 稳定像素比例，越高越好 |

## 🔧 参数调优指南

### 根据视频特征调整

**抖动严重的视频**:
```bash
--alpha 1.2 --use-stable --beta 0.8
```

**运动较多的视频**:
```bash
--alpha 0.5 --beta 0.4 --resid-thr 3.0
```

**低质量/模糊视频**:
```bash
--max-per-second 12 --alpha 0.6 --min-grad 3.0
```

### 根据后续用途调整

**AI模型识别**:
```bash
--max-per-second 8 --alpha 0.7 --downscale 1
```

**人工审查**:
```bash
--max-per-second 5 --alpha 1.0 --use-stable
```

**快速预览**:
```bash
--max-per-second 3 --alpha 1.5 --downscale 2
```

## 🚨 常见问题

### Q: 提取的帧数少于预期怎么办？

A: 尝试以下调整：
- 降低 `--alpha` 值（如0.5）
- 增加 `--max-per-second`
- 降低 `--min-grad` 值
- 增加 `--resid-thr` 值
- 检查是否过多黑白帧被过滤，可调整 `--extreme-pixel-ratio` 到0.9

### Q: 处理速度太慢怎么办？

A: 优化建议：
- 增加 `--downscale` 值
- 增加 `--stable-downscale` 值
- 增加 `--grid-step` 值
- 添加 `--no-exposure` 参数

### Q: 如何确保不遗漏重要内容？

A: 保守配置：
```bash
--max-per-second 10 --alpha 0.4 --beta 0.3 --resid-thr 3.5
```

## 📈 性能参考

| 视频规格 | 处理速度 | 推荐配置 |
|----------|----------|----------|
| 480p 30fps | ~15帧/秒 | 默认配置 |
| 1080p 30fps | ~8帧/秒 | `--downscale 2` |
| 4K 30fps | ~3帧/秒 | `--downscale 4` |

## 🔄 批量处理

对于多个视频文件的批量处理，可以使用shell脚本：

```bash
#!/bin/bash
for video in assets/video/*.mp4; do
    echo "Processing $video..."
    python test/extract_clear_frames.py --input "$video"
done
```

## 📞 技术支持

如需调整参数或遇到问题，请参考：
1. 本文档的参数调优指南
2. 脚本输出的详细日志信息
3. 根据输出文件名中的质量指标进行分析
