#!/usr/bin/env python3
"""
Utility script to extract the clearest frames from a shaky video.  The script
computes a simple quality score for each frame based on a combination of
sharpness (variance of Laplacian) and camera motion (via optical flow).
Frames are grouped into one-second buckets (using the video FPS) and up to
``max_frames_per_second`` frames with the highest scores are retained per
bucket.  Selected frames are saved as image files with metadata encoded in
their filenames.

Usage:
    python extract_clear_frames.py --input <video.mp4> --output <output_dir> \
        --max-per-second 5 --alpha 1.5 --downscale 2

Arguments:
    --input: path to the input video file.
    --output: directory where the extracted frames will be saved.  The
              directory will be created if it does not exist.
    --max-per-second: maximum number of frames to keep per second of video.
    --alpha: penalty factor for motion; higher values penalize frames with
             more motion (larger optical flow magnitudes) more strongly.
    --downscale: factor by which to downscale frames before computing
                 optical flow.  Downscaling speeds up computation and
                 reduces noise; a value of 2 works well for most
                 resolutions.
"""

import argparse
import math
import os
import cv2
import numpy as np
from collections import defaultdict
import heapq
import time
from datetime import datetime


def variance_of_laplacian(gray: np.ndarray) -> float:
    """Compute the variance of the Laplacian of the grayscale image."""
    lap = cv2.Laplacian(gray, cv2.CV_32F, ksize=3)
    return float(lap.var())


def mean_optical_flow_mag(prev_gray: np.ndarray, gray: np.ndarray, downscale: int = 2) -> float:
    """Compute the mean magnitude of optical flow between two grayscale frames.

    Downscales the frames to speed up computation and reduce noise.
    If ``downscale`` is 1, no downscaling is performed.
    """
    if prev_gray is None:
        return 0.0
    if downscale > 1:
        prev_gray_small = cv2.resize(prev_gray, (prev_gray.shape[1] // downscale, prev_gray.shape[0] // downscale))
        gray_small = cv2.resize(gray, (gray.shape[1] // downscale, gray.shape[0] // downscale))
    else:
        prev_gray_small, gray_small = prev_gray, gray

    flow = cv2.calcOpticalFlowFarneback(
        prev=prev_gray_small,
        next=gray_small,
        flow=None,
        pyr_scale=0.5,
        levels=3,
        winsize=15,
        iterations=3,
        poly_n=5,
        poly_sigma=1.2,
        flags=0,
    )
    mag, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])
    return float(np.mean(mag))


def exposure_weight(gray: np.ndarray) -> float:
    """Compute a weight based on exposure: penalize frames that are too dark or too bright.

    Returns a value between 0.6 and 1.0.  Uses histogram bins for very dark
    (0-15) and very bright (240-255) pixels; penalizes high proportions of
    these pixels.
    """
    hist = cv2.calcHist([gray], [0], None, [256], [0, 256]).ravel()
    total = hist.sum() or 1.0
    hist /= total
    dark = hist[:16].sum()
    bright = hist[240:].sum()
    penalty = 0.2 * dark + 0.2 * bright
    return float(max(0.6, 1.0 - penalty))


def is_black_or_white_frame(
    gray: np.ndarray,
    *,
    black_threshold: int = 15,
    white_threshold: int = 240,
    extreme_pixel_ratio: float = 0.85
) -> tuple[bool, str]:
    """Check if a frame is predominantly black or white.

    Parameters:
        gray: grayscale image
        black_threshold: pixel values below this are considered black
        white_threshold: pixel values above this are considered white
        extreme_pixel_ratio: minimum ratio of extreme pixels to classify as black/white frame

    Returns:
        (is_extreme, frame_type) where frame_type is 'black', 'white', or 'normal'
    """
    total_pixels = gray.size

    # Count black and white pixels
    black_pixels = np.sum(gray <= black_threshold)
    white_pixels = np.sum(gray >= white_threshold)

    black_ratio = black_pixels / total_pixels
    white_ratio = white_pixels / total_pixels

    # Check if frame is predominantly black or white
    if black_ratio >= extreme_pixel_ratio:
        return True, 'black'
    elif white_ratio >= extreme_pixel_ratio:
        return True, 'white'
    else:
        return False, 'normal'


def stable_mask_and_ratio(
    prev_gray: np.ndarray | None,
    gray: np.ndarray,
    *,
    grid_step: int = 16,
    model: str = "affine",
    resid_thr: float = 1.5,
    min_grad: float = 10.0,
    stable_downscale: int = 1,
) -> tuple[np.ndarray, float]:
    """Estimate a stable-pixel mask and ratio between consecutive frames.

    Parameters:
        prev_gray: previous grayscale frame; if ``None`` return all zeros mask and ratio 1.0.
        gray: current grayscale frame.
        grid_step: spacing between sampled points in the grid for estimating motion.
        model: motion model ('affine' or 'homography').
        resid_thr: RANSAC reprojection threshold in pixels.
        min_grad: minimum gradient magnitude to consider a point; low-texture points
            are unreliable and ignored.

    Returns:
        mask: binary mask (uint8) where 255 indicates a stable region (inliers)
        stable_ratio: fraction of pixels estimated to be stable, in [0, 1].  If
            no points are found, returns 1.0 (treat frame as fully stable).
    """
    if prev_gray is None:
        # At the first frame we cannot compute stability; assume fully stable.
        mask = np.full_like(gray, 255, dtype=np.uint8)
        return mask, 1.0
    # Optionally downscale frames for stability estimation.  Downscaling
    # significantly speeds up flow and RANSAC computations with minimal
    # impact on the stable ratio.
    if stable_downscale > 1:
        prev_small = cv2.resize(prev_gray, (prev_gray.shape[1] // stable_downscale, prev_gray.shape[0] // stable_downscale))
        gray_small = cv2.resize(gray, (gray.shape[1] // stable_downscale, gray.shape[0] // stable_downscale))
    else:
        prev_small, gray_small = prev_gray, gray
    # Compute dense optical flow on the (possibly downscaled) frames
    flow = cv2.calcOpticalFlowFarneback(
        prev=prev_small,
        next=gray_small,
        flow=None,
        pyr_scale=0.5,
        levels=3,
        winsize=21,
        iterations=3,
        poly_n=5,
        poly_sigma=1.2,
        flags=0,
    )
    H, W = gray_small.shape
    # Sample points on a regular grid in the small frame
    ys, xs = np.mgrid[grid_step // 2:H:grid_step, grid_step // 2:W:grid_step]
    xs = xs.reshape(-1).astype(np.float32)
    ys = ys.reshape(-1).astype(np.float32)
    pts0 = np.stack([xs, ys], axis=1)
    # Flow vectors at sampled points
    flow_samp = flow[ys.astype(int), xs.astype(int)]
    pts1 = pts0 + flow_samp
    # Compute gradient magnitude at sampled points (use original small frame)
    gx = cv2.Sobel(prev_small, cv2.CV_32F, 1, 0, ksize=3)
    gy = cv2.Sobel(prev_small, cv2.CV_32F, 0, 1, ksize=3)
    grad_mag = np.hypot(gx[ys.astype(int), xs.astype(int)], gy[ys.astype(int), xs.astype(int)])
    good = grad_mag > min_grad
    pts0 = pts0[good]
    pts1 = pts1[good]
    if len(pts0) < 6:
        mask_full = np.full_like(gray, 255, dtype=np.uint8)
        return mask_full, 1.0
    # Estimate global motion using RANSAC on downscaled coordinates
    if model == 'homography':
        Hmat, inliers = cv2.findHomography(pts0, pts1, cv2.RANSAC, ransacReprojThreshold=resid_thr)
        inlier_mask = inliers.ravel().astype(bool) if inliers is not None else np.zeros(len(pts0), dtype=bool)
    else:
        A, inliers = cv2.estimateAffine2D(pts0, pts1, method=cv2.RANSAC, ransacReprojThreshold=resid_thr)
        inlier_mask = inliers.ravel().astype(bool) if inliers is not None else np.zeros(len(pts0), dtype=bool)
    # Create a mask marking stable regions around inliers in the small frame
    mask_small = np.zeros((H, W), dtype=np.uint8)
    half = grid_step // 2
    for (x, y), ok in zip(pts0.astype(int), inlier_mask):
        if not ok:
            continue
        x0, y0 = max(0, x - half), max(0, y - half)
        x1, y1 = min(W, x + half), min(H, y + half)
        mask_small[y0:y1, x0:x1] = 255
    # Upscale the small mask back to the original frame size
    mask_full = cv2.resize(mask_small, (gray.shape[1], gray.shape[0]), interpolation=cv2.INTER_NEAREST)
    stable_ratio = float(mask_small.mean()) / 255.0
    return mask_full, stable_ratio


def frame_quality_score(
    gray: np.ndarray,
    prev_gray: np.ndarray | None,
    *,
    alpha: float = 1.5,
    downscale: int = 2,
    use_exposure: bool = True,
    use_stable: bool = False,
    beta: float = 1.0,
    grid_step: int = 16,
    model: str = "affine",
    resid_thr: float = 1.5,
    min_grad: float = 10.0,
    stable_downscale: int = 1,
    filter_black_white: bool = True,
    black_threshold: int = 15,
    white_threshold: int = 240,
    extreme_pixel_ratio: float = 0.85,
) -> tuple[float, dict]:
    """Compute a composite quality score for a frame with optional stable-pixel weighting.

    The score combines several factors:
        - Sharpness (variance of Laplacian)
        - Motion penalty (mean optical flow magnitude)
        - Exposure weight (downweights overly bright/dark frames)
        - Stable-pixel ratio (fraction of pixels consistent with global motion)
        - Black/white frame filtering (eliminates pure black or white frames)

    Parameters:
        gray: current grayscale frame.
        prev_gray: previous grayscale frame for computing motion and stability.
        alpha: exponential penalty factor for motion (higher alpha penalizes motion more).
        downscale: downscale factor for optical flow computation.
        use_exposure: whether to include exposure weighting.
        use_stable: whether to compute and include stable-pixel ratio in scoring.
        beta: exponent for stable ratio weighting; score multiplied by (stable_ratio ** beta).
        grid_step: spacing between sampled points when estimating stable pixels.
        model: motion model for stability estimation ('affine' or 'homography').
        resid_thr: RANSAC reprojection threshold for stability estimation.
        min_grad: minimum gradient magnitude to consider a point in stability estimation.
        filter_black_white: whether to filter out black/white frames.
        black_threshold: pixel values below this are considered black.
        white_threshold: pixel values above this are considered white.
        extreme_pixel_ratio: minimum ratio of extreme pixels to classify as black/white frame.

    Returns:
        (score, meta) where meta contains the raw metrics: sharpness, flow, expo_w,
        stable_ratio, frame_type.
    """
    # Check for black/white frames first
    is_extreme, frame_type = False, 'normal'
    if filter_black_white:
        is_extreme, frame_type = is_black_or_white_frame(
            gray,
            black_threshold=black_threshold,
            white_threshold=white_threshold,
            extreme_pixel_ratio=extreme_pixel_ratio
        )

    # If it's a black or white frame, return very low score
    if is_extreme:
        return 0.0, {
            "sharpness": 0.0,
            "flow": 0.0,
            "expo_w": 0.0,
            "stable_ratio": 0.0,
            "frame_type": frame_type,
        }

    sharpness = variance_of_laplacian(gray)
    flow = mean_optical_flow_mag(prev_gray, gray, downscale=downscale)
    expo = exposure_weight(gray) if use_exposure else 1.0
    # Base score penalised by motion and exposure
    base = sharpness * math.exp(-alpha * flow) * expo
    stable_ratio = 1.0
    if use_stable:
        # Compute stable pixel ratio; ignore mask to avoid heavy per-frame mask storage
        _, stable_ratio = stable_mask_and_ratio(
            prev_gray,
            gray,
            grid_step=grid_step,
            model=model,
            resid_thr=resid_thr,
            min_grad=min_grad,
            stable_downscale=stable_downscale,
        )
        # Clamp stable_ratio to [0,1] to avoid numerical issues
        stable_ratio = max(0.0, min(1.0, stable_ratio))
        # Weight the base score by the stable ratio raised to beta
        base *= stable_ratio ** beta
    return base, {
        "sharpness": sharpness,
        "flow": flow,
        "expo_w": expo,
        "stable_ratio": stable_ratio,
        "frame_type": frame_type,
    }


def select_frames(
    video_path: str,
    max_frames_per_second: int = 5,
    *,
    alpha: float = 1.5,
    downscale: int = 2,
    use_exposure: bool = True,
    use_stable: bool = False,
    beta: float = 1.0,
    grid_step: int = 16,
    model: str = "affine",
    resid_thr: float = 1.5,
    min_grad: float = 10.0,
    stable_downscale: int = 1,
    filter_black_white: bool = True,
    black_threshold: int = 15,
    white_threshold: int = 240,
    extreme_pixel_ratio: float = 0.85,
) -> list[tuple[int, float, float, dict]]:
    """Process the video and select up to ``max_frames_per_second`` frames per second.

    Frames are scored using ``frame_quality_score`` with the provided parameters.

    Returns:
        A list of tuples (frame_index, timestamp_sec, score, meta), sorted by
        frame index.
    """
    print(f"🎬 开始处理视频: {video_path}")
    start_time = time.time()

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise RuntimeError(f"Failed to open video: {video_path}")

    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = total_frames / fps
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    print(f"📊 视频信息:")
    print(f"   - 分辨率: {width}x{height}")
    print(f"   - 帧率: {fps:.1f} fps")
    print(f"   - 总帧数: {total_frames}")
    print(f"   - 时长: {duration:.1f} 秒")
    print(f"   - 预计提取帧数: {int(duration * max_frames_per_second)}")
    print()

    prev_gray = None
    # Use a dictionary mapping second index to a min-heap of fixed size.
    buckets: dict[int, list[tuple[float, int, float, dict]]] = defaultdict(list)
    frame_idx = 0
    last_progress_time = time.time()

    # Statistics for black/white frame filtering
    black_frames = 0
    white_frames = 0

    print("🔍 开始分析帧质量...")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 进度显示
        current_time = time.time()
        if current_time - last_progress_time >= 2.0 or frame_idx % 300 == 0:  # 每2秒或每300帧显示一次
            progress = (frame_idx / total_frames) * 100
            elapsed = current_time - start_time
            if frame_idx > 0:
                eta = (elapsed / frame_idx) * (total_frames - frame_idx)
                print(f"   进度: {progress:.1f}% ({frame_idx}/{total_frames}) - "
                      f"已用时: {elapsed:.1f}s - 预计剩余: {eta:.1f}s")
            last_progress_time = current_time

        # Compute grayscale for quality assessment
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        gray = gray.astype(np.uint8)
        t = frame_idx / fps
        sec_idx = int(t)
        score, meta = frame_quality_score(
            gray=gray,
            prev_gray=prev_gray,
            alpha=alpha,
            downscale=downscale,
            use_exposure=use_exposure,
            use_stable=use_stable,
            beta=beta,
            grid_step=grid_step,
            model=model,
            resid_thr=resid_thr,
            min_grad=min_grad,
            stable_downscale=stable_downscale,
            filter_black_white=filter_black_white,
            black_threshold=black_threshold,
            white_threshold=white_threshold,
            extreme_pixel_ratio=extreme_pixel_ratio,
        )

        # Count filtered frames for statistics
        if filter_black_white and 'frame_type' in meta:
            if meta['frame_type'] == 'black':
                black_frames += 1
            elif meta['frame_type'] == 'white':
                white_frames += 1

        # Only add frames with non-zero scores (i.e., not black/white frames)
        if score > 0:
            # Maintain a min-heap; Python's heapq is a min-heap, so store (score, ...)
            heap = buckets[sec_idx]
            if len(heap) < max_frames_per_second:
                heapq.heappush(heap, (score, frame_idx, t, meta))
            else:
                # If current score is better than the smallest in heap, replace
                if score > heap[0][0]:
                    heapq.heapreplace(heap, (score, frame_idx, t, meta))
        prev_gray = gray
        frame_idx += 1
    cap.release()

    # 最终进度
    total_time = time.time() - start_time
    print(f"✅ 帧分析完成! 总用时: {total_time:.1f}s")

    # 显示过滤统计
    if filter_black_white:
        total_filtered = black_frames + white_frames
        print(f"🚫 过滤统计: 黑帧 {black_frames} 个, 白帧 {white_frames} 个, 总计过滤 {total_filtered} 个")
        if total_filtered > 0:
            filter_ratio = (total_filtered / total_frames) * 100
            print(f"   过滤比例: {filter_ratio:.1f}%")

    # Flatten heaps into a list and sort by frame index
    selected: list[tuple[int, float, float, dict]] = []
    for heap in buckets.values():
        for score, idx, t, meta in heap:
            selected.append((idx, t, score, meta))
    selected.sort(key=lambda x: x[0])

    print(f"🎯 选择了 {len(selected)} 个最佳帧 (目标: {int(duration * max_frames_per_second)})")
    return selected


def save_selected_frames(
    video_path: str,
    selected: list[tuple[int, float, float, dict]],
    output_dir: str,
) -> None:
    """Save the selected frames from the video to the given output directory.
    Filenames include frame index, timestamp, score, sharpness and flow.
    """
    print(f"💾 开始保存帧到目录: {output_dir}")
    start_time = time.time()

    os.makedirs(output_dir, exist_ok=True)
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise RuntimeError(f"Failed to open video: {video_path}")

    index_set = {idx for idx, _, _, _ in selected}
    selected_map = {idx: (t, score, meta) for idx, t, score, meta in selected}
    frame_idx = 0
    saved = 0
    total_to_save = len(selected)
    last_progress_time = time.time()

    print(f"🎯 需要保存 {total_to_save} 帧")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if frame_idx in index_set:
            t, score, meta = selected_map[frame_idx]

            # 进度显示
            current_time = time.time()
            if current_time - last_progress_time >= 1.0 or saved % 50 == 0:  # 每1秒或每50帧显示一次
                progress = (saved / total_to_save) * 100
                print(f"   保存进度: {progress:.1f}% ({saved}/{total_to_save})")
                last_progress_time = current_time

            # Compose filename with metadata
            # Compose filename with metadata. Include stable_ratio if available.
            # Use 'sr' prefix for stable ratio. If stable_ratio not in meta, omit it.
            stable_ratio_str = ""
            if 'stable_ratio' in meta:
                stable_ratio_str = f"_sr{meta['stable_ratio']:.3f}"
            fname = (
                f"frame_{frame_idx:06d}_t{t:.2f}s_"
                f"S{score:.0f}_sh{meta['sharpness']:.0f}_fl{meta['flow']:.3f}"
                f"{stable_ratio_str}.jpg"
            )
            out_path = os.path.join(output_dir, fname)
            cv2.imwrite(out_path, frame)
            saved += 1
            if saved >= len(selected):
                break
        frame_idx += 1
    cap.release()

    save_time = time.time() - start_time
    print(f"✅ 保存完成! 共保存 {saved} 帧，用时: {save_time:.1f}s")
    print(f"📁 输出目录: {output_dir}")


def main() -> None:
    parser = argparse.ArgumentParser(description="Extract the clearest frames from a shaky video.")
    parser.add_argument("--input", default="assets/video/inspection.mp4", help="Input video file path (default: assets/video/inspection.mp4)")
    parser.add_argument("--output", help="Output directory for extracted frames (default: auto-generated in assets/)")
    parser.add_argument(
        "--max-per-second",
        type=int,
        default=8,
        help="Maximum number of frames to select per second (default: 8)",
    )
    parser.add_argument(
        "--alpha",
        type=float,
        default=0.7,
        help="Motion penalty strength for optical flow (default: 0.7)",
    )
    parser.add_argument(
        "--downscale",
        type=int,
        default=1,
        help="Downscale factor for optical flow computation (default: 1)",
    )
    parser.add_argument(
        "--no-exposure",
        action="store_true",
        help="Disable exposure weighting (slightly faster)",
    )
    parser.add_argument(
        "--use-stable",
        action="store_true",
        default=True,
        help="Use stable-pixel ratio to weight the quality score (default: True)."
        " Stable pixels are those consistent with global motion; frames with more"
        " stable pixels will score higher."
    )
    parser.add_argument(
        "--beta",
        type=float,
        default=0.6,
        help="Exponent for stable ratio weighting; score multiplied by (stable_ratio ** beta)"
        " when --use-stable is specified (default: 0.6)",
    )
    parser.add_argument(
        "--grid-step",
        type=int,
        default=12,
        help="Grid spacing in pixels for stable pixel estimation (default: 12)",
    )
    parser.add_argument(
        "--model",
        choices=["affine", "homography"],
        default="affine",
        help="Motion model to use for stability estimation (default: affine)",
    )
    parser.add_argument(
        "--resid-thr",
        type=float,
        default=2.5,
        help="RANSAC reprojection threshold in pixels for stability estimation (default: 2.5)",
    )
    parser.add_argument(
        "--min-grad",
        type=float,
        default=6.0,
        help="Minimum gradient magnitude threshold for points used in stability estimation (default: 6.0)",
    )
    parser.add_argument(
        "--stable-downscale",
        type=int,
        default=1,
        help="Downscale factor for frames when computing stable pixel ratio. "
        "Increasing this (e.g. 2) speeds up stability estimation at the cost of precision (default: 1)",
    )
    parser.add_argument(
        "--no-filter-black-white",
        action="store_true",
        help="Disable filtering of black and white frames",
    )
    parser.add_argument(
        "--black-threshold",
        type=int,
        default=15,
        help="Pixel values below this are considered black (default: 15)",
    )
    parser.add_argument(
        "--white-threshold",
        type=int,
        default=240,
        help="Pixel values above this are considered white (default: 240)",
    )
    parser.add_argument(
        "--extreme-pixel-ratio",
        type=float,
        default=0.85,
        help="Minimum ratio of extreme pixels to classify as black/white frame (default: 0.85)",
    )

    args = parser.parse_args()

    # 生成输出目录名（如果未指定）
    if args.output is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"assets/extracted_frames_{timestamp}"

    print("=" * 60)
    print("🚄 高铁站台巡检视频帧提取工具")
    print("=" * 60)
    print(f"📥 输入视频: {args.input}")
    print(f"📤 输出目录: {args.output}")
    print(f"⚙️  参数配置:")
    print(f"   - 每秒最大帧数: {args.max_per_second}")
    print(f"   - 运动惩罚系数: {args.alpha}")
    print(f"   - 降采样因子: {args.downscale}")
    print(f"   - 稳定性分析: {'启用' if args.use_stable else '禁用'}")
    if args.use_stable:
        print(f"   - 稳定性权重: {args.beta}")
        print(f"   - 网格间距: {args.grid_step}")
        print(f"   - 运动模型: {args.model}")
    print(f"   - 曝光权重: {'禁用' if args.no_exposure else '启用'}")
    print(f"   - 黑白帧过滤: {'禁用' if args.no_filter_black_white else '启用'}")
    if not args.no_filter_black_white:
        print(f"   - 黑帧阈值: {args.black_threshold}")
        print(f"   - 白帧阈值: {args.white_threshold}")
        print(f"   - 极值像素比例: {args.extreme_pixel_ratio}")
    print()

    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        print(f"❌ 错误: 输入视频文件不存在: {args.input}")
        return

    start_total = time.time()
    use_exposure = not args.no_exposure
    filter_black_white = not args.no_filter_black_white

    try:
        selected = select_frames(
            video_path=args.input,
            max_frames_per_second=args.max_per_second,
            alpha=args.alpha,
            downscale=args.downscale,
            use_exposure=use_exposure,
            use_stable=args.use_stable,
            beta=args.beta,
            grid_step=args.grid_step,
            model=args.model,
            resid_thr=args.resid_thr,
            min_grad=args.min_grad,
            stable_downscale=args.stable_downscale,
            filter_black_white=filter_black_white,
            black_threshold=args.black_threshold,
            white_threshold=args.white_threshold,
            extreme_pixel_ratio=args.extreme_pixel_ratio,
        )

        print()
        save_selected_frames(
            video_path=args.input,
            selected=selected,
            output_dir=args.output,
        )

        total_time = time.time() - start_total
        print()
        print("=" * 60)
        print("🎉 提取完成!")
        print(f"📊 统计信息:")
        print(f"   - 总用时: {total_time:.1f} 秒")
        print(f"   - 提取帧数: {len(selected)}")
        print(f"   - 输出目录: {args.output}")
        print(f"   - 平均处理速度: {len(selected)/total_time:.1f} 帧/秒")
        print("=" * 60)

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    main()