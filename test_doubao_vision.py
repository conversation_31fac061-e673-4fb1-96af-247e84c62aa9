#!/usr/bin/env python3
"""
测试豆包视觉模型识别图片的能力
参照click-pilot项目的视觉模型使用方式
"""

import base64
import os
from pathlib import Path
from utils import get_chat_model


def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64格式"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def test_doubao_vision_basic():
    """基础视觉识别测试"""
    print("🔍 基础视觉识别测试")
    print("-" * 40)
    
    # 检查图片文件是否存在
    image_path = "assets/3.jpg"
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print(f"📷 分析图片: {image_path}")
    
    try:
        # 获取视觉模型
        vision_model = get_chat_model("seed")
        print(f"✅ 视觉模型加载成功: {type(vision_model).__name__}")
        
        # 编码图片
        base64_image = encode_image_to_base64(image_path)
        print(f"✅ 图片编码完成，大小: {len(base64_image)} 字符")
        
        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请详细描述这张图片的内容，包括你看到的所有物体、场景、文字等信息。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
        
        # 发送请求
        print("🤖 正在分析图片...")
        response = vision_model.invoke(messages)
        
        print("✅ 分析完成!")
        print("\n📝 识别结果:")
        print("=" * 60)
        print(response.content)
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 视觉识别失败: {str(e)}")


def test_doubao_vision_specific_tasks():
    """特定任务的视觉识别测试"""
    print("\n🎯 特定任务视觉识别测试")
    print("-" * 40)
    
    image_path = "assets/1.png"
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    try:
        vision_model = get_chat_model("ui_tars")
        base64_image = encode_image_to_base64(image_path)
        
        # 测试不同的识别任务
        tasks = [
            {
                "name": "文字识别",
                "prompt": "请识别图片中的所有文字内容，如果有的话。"
            },
            {
                "name": "场景分析",
                "prompt": "请分析这是什么场景或环境？"
            },
        ]
        
        for task in tasks:
            print(f"\n🔍 {task['name']}:")
            print("-" * 20)
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": task["prompt"]
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            response = vision_model.invoke(messages)
            print(response.content)
            
    except Exception as e:
        print(f"❌ 特定任务识别失败: {str(e)}")


def test_doubao_vision_streaming():
    """流式响应测试"""
    print("\n📡 流式响应测试")
    print("-" * 40)
    
    image_path = "assets/1.png"
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    try:
        vision_model = get_chat_model("ui_tars")
        base64_image = encode_image_to_base64(image_path)
        
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请逐步分析这张图片，先说整体印象，再说具体细节。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]
        
        print("🤖 流式分析中...")
        print("📝 实时响应:")
        print("-" * 30)
        
        for chunk in vision_model.stream(messages):
            print(chunk.content, end="", flush=True)
        
        print("\n" + "-" * 30)
        print("✅ 流式响应完成!")
        
    except Exception as e:
        print(f"❌ 流式响应失败: {str(e)}")


def test_multiple_images():
    """测试一次性提交多张图片识别"""
    print("\n🖼️  一次性多图片识别测试")
    print("-" * 40)

    # 指定要测试的图片
    image_paths = ["assets/1.png", "assets/2_converted.png"]

    # 确保转换后的图片存在
    if not os.path.exists("assets/2_converted.png") and os.path.exists("assets/2.png"):
        try:
            from PIL import Image
            img = Image.open('assets/2.png')
            img.save('assets/2_converted.png', 'PNG')
            print("✅ 已转换 2.png 为 PNG 格式")
        except:
            image_paths = ["assets/1.png"]

    # 检查文件存在性
    existing_images = [img for img in image_paths if os.path.exists(img)]

    if len(existing_images) < 2:
        print("❌ 需要至少2张图片")
        return

    print(f"📷 一次性提交 {len(existing_images)} 张图片:")
    for img in existing_images:
        print(f"   - {img}")

    try:
        vision_model = get_chat_model("vision")

        # 构建包含多张图片的单个消息
        content = [
            {
                "type": "text",
                "text": f"我一次性发送{len(existing_images)}张图片给你，分别描述每张图片的内容。"
            }
        ]

        # 将所有图片添加到同一个消息中
        for image_path in existing_images:
            base64_image = encode_image_to_base64(image_path)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{base64_image}"
                }
            })

        messages = [{"role": "user", "content": content}]

        print("🤖 正在一次性分析所有图片...")
        response = vision_model.invoke(messages)

        print("✅ 一次性多图片分析完成!")
        print("\n📝 分析结果:")
        print("=" * 60)
        print(response.content)
        print("=" * 60)

    except Exception as e:
        print(f"❌ 多图片识别失败: {str(e)}")


def main():
    """主测试函数"""
    print("=" * 60)
    print("🚄 豆包视觉模型测试 - 高铁项目")
    print("=" * 60)
    
    # 检查模型可用性
    try:
        vision_model = get_chat_model("ui_tars")
        print(f"✅ 豆包视觉模型初始化成功: {type(vision_model).__name__}")
    except Exception as e:
        print(f"❌ 豆包视觉模型初始化失败: {str(e)}")
        return
    
    # 运行各项测试
    test_doubao_vision_basic()
    # test_doubao_vision_specific_tasks()
    # test_doubao_vision_streaming()
    # test_multiple_images()
    
    print("\n" + "=" * 60)
    print("✅ 豆包视觉模型测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
