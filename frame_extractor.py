#!/usr/bin/env python3
"""
高铁巡检帧提取器
每秒保留最高清且稳定的10张图片
"""

import cv2
import numpy as np
import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple


class InspectionFrameExtractor:
    """高铁巡检专用帧提取器"""
    
    def __init__(self, video_path: str = "assets/video/inspection.mp4"):
        self.video_path = video_path
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = Path(f"assets/frames_{self.timestamp}")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 检查视频
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频: {video_path}")
        
        # 获取视频信息
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        self.duration = self.total_frames / self.fps
        
        print(f"🚄 高铁巡检帧提取器")
        print(f"视频: {video_path}")
        print(f"分辨率: {self.width}x{self.height}")
        print(f"帧率: {self.fps:.1f} FPS")
        print(f"时长: {self.duration:.1f}秒")
        print(f"目标: 每秒10帧，共约{int(self.duration * 10)}帧")
        print(f"输出: {self.output_dir}")
        print("=" * 50)
    
    def calculate_quality_score(self, frame: np.ndarray) -> Dict[str, float]:
        """计算帧的综合质量评分"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 1. 清晰度评估 - 拉普拉斯方差
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        sharpness = laplacian.var()
        
        # 2. 边缘清晰度 - Sobel算子
        sobel_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        edge_strength = np.sqrt(sobel_x**2 + sobel_y**2).mean()
        
        # 3. 梯度幅值
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2).mean()
        
        # 4. 稳定性评估 - 运动模糊检测
        motion_blur_score = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # 5. 曝光稳定性
        brightness = gray.mean()
        brightness_std = gray.std()
        exposure_score = 255 - abs(brightness - 128)  # 接近中等亮度得分高
        
        # 6. 对比度检查
        contrast = brightness_std
        
        # 7. 异常检测
        is_abnormal = (brightness < 10 or brightness > 245 or 
                      brightness_std < 5 or sharpness < 10)
        
        # 综合评分 (权重可调整)
        if is_abnormal:
            total_score = 0  # 异常帧直接0分
        else:
            total_score = (
                sharpness * 0.3 +           # 清晰度权重最高
                edge_strength * 0.25 +      # 边缘清晰度
                gradient_magnitude * 0.2 +  # 梯度幅值
                motion_blur_score * 0.1 +   # 运动模糊
                exposure_score * 0.1 +      # 曝光稳定性
                contrast * 0.05             # 对比度
            )
        
        return {
            'total_score': total_score,
            'sharpness': sharpness,
            'edge_strength': edge_strength,
            'gradient_magnitude': gradient_magnitude,
            'motion_blur_score': motion_blur_score,
            'brightness': brightness,
            'contrast': contrast,
            'is_abnormal': is_abnormal
        }
    
    def analyze_video_quality(self) -> Dict:
        """预分析视频整体质量分布"""
        print("🔍 预分析视频质量...")
        
        # 采样策略：每5帧采样一次
        sample_interval = max(1, int(self.fps // 6))
        
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        quality_scores = []
        frame_count = 0
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            if frame_count % sample_interval == 0:
                quality = self.calculate_quality_score(frame)
                quality_scores.append(quality['total_score'])
            
            frame_count += 1
            
            if frame_count % (int(self.fps) * 30) == 0:
                print(f"  预分析进度: {frame_count/self.fps:.0f}秒")
        
        quality_scores = [s for s in quality_scores if s > 0]  # 排除异常帧
        
        if not quality_scores:
            raise ValueError("视频中没有找到合格的帧")
        
        analysis = {
            'mean_quality': np.mean(quality_scores),
            'std_quality': np.std(quality_scores),
            'min_quality': np.min(quality_scores),
            'max_quality': np.max(quality_scores),
            'quality_threshold': np.percentile(quality_scores, 30),  # 30%分位数作为基础阈值
            'sample_count': len(quality_scores)
        }
        
        print(f"✅ 预分析完成 (采样{len(quality_scores)}帧)")
        print(f"质量范围: {analysis['min_quality']:.1f} - {analysis['max_quality']:.1f}")
        print(f"平均质量: {analysis['mean_quality']:.1f}")
        print(f"质量阈值: {analysis['quality_threshold']:.1f}")
        
        return analysis
    
    def extract_frames_per_second(self, analysis: Dict) -> List[Dict]:
        """按秒提取最佳10帧"""
        print("🎯 开始按秒提取最佳帧...")
        
        quality_threshold = analysis['quality_threshold']
        
        # 重置视频到开始
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        all_frames = []
        frame_count = 0
        
        # 收集所有帧的质量信息
        print("扫描所有帧...")
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
            
            timestamp = frame_count / self.fps
            second = int(timestamp)
            
            quality = self.calculate_quality_score(frame)
            
            # 只保留质量达标的帧
            if quality['total_score'] >= quality_threshold:
                all_frames.append({
                    'frame_number': frame_count,
                    'timestamp': timestamp,
                    'second': second,
                    'quality': quality,
                    'frame': frame.copy()  # 直接保存帧数据
                })
            
            frame_count += 1
            
            if frame_count % (int(self.fps) * 30) == 0:
                print(f"  扫描进度: {frame_count/self.fps:.0f}秒")
        
        print(f"质量筛选后剩余: {len(all_frames)} 帧")
        
        # 按秒分组
        frames_by_second = {}
        for frame_info in all_frames:
            second = frame_info['second']
            if second not in frames_by_second:
                frames_by_second[second] = []
            frames_by_second[second].append(frame_info)
        
        # 每秒选择最佳10帧
        selected_frames = []
        total_seconds = len(frames_by_second)
        
        for second, frames_in_second in frames_by_second.items():
            # 按质量评分排序
            frames_in_second.sort(key=lambda x: x['quality']['total_score'], reverse=True)
            
            # 选择最多10帧，但要考虑时间分布
            frames_to_select = min(10, len(frames_in_second))
            
            if frames_to_select <= 10:
                # 如果帧数不超过10，全部选择
                selected_in_second = frames_in_second[:frames_to_select]
            else:
                # 如果超过10帧，选择质量最高的10帧，但要保证时间分布
                # 先选择质量最高的10帧
                selected_in_second = frames_in_second[:10]
            
            # 按时间戳排序，确保时间顺序
            selected_in_second.sort(key=lambda x: x['timestamp'])
            
            selected_frames.extend(selected_in_second)
            
            if second % 30 == 0:
                print(f"  处理进度: {second}秒, 该秒选择{len(selected_in_second)}帧")
        
        print(f"✅ 提取完成!")
        print(f"总秒数: {total_seconds}")
        print(f"选中帧数: {len(selected_frames)}")
        print(f"平均每秒: {len(selected_frames)/total_seconds:.1f} 帧")
        
        return selected_frames
    
    def save_frames(self, frames: List[Dict]) -> Path:
        """保存提取的帧"""
        frames_dir = self.output_dir / "frames"
        frames_dir.mkdir(exist_ok=True)
        
        print(f"💾 保存 {len(frames)} 帧...")
        
        for i, frame_data in enumerate(frames):
            filename = (f"frame_{i+1:06d}_"
                       f"s{int(frame_data['timestamp'])}_{frame_data['timestamp']%1:.2f}_"
                       f"q{frame_data['quality']['total_score']:.0f}.jpg")
            
            filepath = frames_dir / filename
            cv2.imwrite(str(filepath), frame_data['frame'], 
                       [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        print(f"✅ 帧已保存到: {frames_dir}")
        return frames_dir
    
    def create_report(self, analysis: Dict, frames: List[Dict]) -> str:
        """创建提取报告"""
        # 统计每秒的帧数
        frames_per_second = {}
        for frame in frames:
            second = int(frame['timestamp'])
            frames_per_second[second] = frames_per_second.get(second, 0) + 1
        
        report = {
            'extraction_info': {
                'timestamp': self.timestamp,
                'video_path': self.video_path,
                'total_frames_extracted': len(frames),
                'target_fps': 10,
                'actual_avg_fps': len(frames) / self.duration,
                'extraction_percentage': (len(frames) / self.total_frames) * 100
            },
            'video_info': {
                'resolution': f"{self.width}x{self.height}",
                'original_fps': self.fps,
                'total_frames': self.total_frames,
                'duration_seconds': self.duration
            },
            'quality_analysis': analysis,
            'frames_per_second_stats': {
                'min_frames_per_second': min(frames_per_second.values()) if frames_per_second else 0,
                'max_frames_per_second': max(frames_per_second.values()) if frames_per_second else 0,
                'avg_frames_per_second': np.mean(list(frames_per_second.values())) if frames_per_second else 0,
                'seconds_with_10_frames': sum(1 for count in frames_per_second.values() if count == 10),
                'total_seconds_processed': len(frames_per_second)
            },
            'quality_summary': {
                'avg_quality_score': np.mean([f['quality']['total_score'] for f in frames]),
                'min_quality_score': np.min([f['quality']['total_score'] for f in frames]),
                'max_quality_score': np.max([f['quality']['total_score'] for f in frames])
            }
        }
        
        report_path = self.output_dir / "extraction_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 提取报告: {report_path}")
        return str(report_path)
    
    def run_extraction(self):
        """运行完整的提取流程"""
        try:
            # 1. 预分析视频质量
            analysis = self.analyze_video_quality()
            
            # 2. 按秒提取最佳帧
            frames = self.extract_frames_per_second(analysis)
            
            if not frames:
                print("❌ 未提取到任何帧")
                return None
            
            # 3. 保存帧
            frames_dir = self.save_frames(frames)
            
            # 4. 创建报告
            report_path = self.create_report(analysis, frames)
            
            # 5. 输出摘要
            print("\n" + "=" * 60)
            print("🎉 提取完成!")
            print(f"📁 帧目录: {frames_dir}")
            print(f"📄 提取报告: {report_path}")
            print(f"🎯 提取帧数: {len(frames):,}")
            print(f"⏱️  平均每秒: {len(frames)/self.duration:.1f} 帧")
            print(f"💾 存储减少: {(1-len(frames)/self.total_frames)*100:.1f}%")
            print("=" * 60)
            
            return {
                'frames_dir': frames_dir,
                'report_path': report_path,
                'frame_count': len(frames)
            }
            
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return None
        
        finally:
            if hasattr(self, 'cap'):
                self.cap.release()


def main():
    """主函数"""
    print("🚄 高铁巡检帧提取器 - 每秒10帧高清版")
    print("=" * 60)
    
    video_path = "assets/video/inspection.mp4"
    
    if not os.path.exists(video_path):
        print(f"❌ 找不到视频文件: {video_path}")
        return 1
    
    try:
        extractor = InspectionFrameExtractor(video_path)
        result = extractor.run_extraction()
        
        if result:
            print("\n💡 使用建议:")
            print("- 提取的帧已按时间顺序命名，便于后续处理")
            print("- 每帧文件名包含时间戳和质量评分")
            print("- 可直接用于目标识别和缺陷检测")
            return 0
        else:
            return 1
            
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
