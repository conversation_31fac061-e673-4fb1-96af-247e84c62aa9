"""TOML configuration source for pydantic settings."""

import os
import sys
import re
from pathlib import Path
from typing import Any

from pydantic_settings import BaseSettings, InitSettingsSource
from pydantic_settings.sources import ConfigFileSourceMixin, PathType


class TomlConfigByEnvSettingsSource(InitSettingsSource, ConfigFileSourceMixin):
    """
    A source class that loads variables from a TOML file
    """

    def __init__(
            self,
            settings_cls: type[BaseSettings],
            toml_dir: PathType | None = None,
    ):
        # 加载.env文件
        from dotenv import load_dotenv
        load_dotenv()

        env_ = os.environ.get("HIGH_SPEED_TRAIN_ENV", "no_prod")
        path = Path(toml_dir)  # type: ignore
        config = {}
        raw_config = self._read_file(path.joinpath(f"{env_}.toml"))
        config.update(self._substitute_env_vars(raw_config))
        super().__init__(settings_cls, config)

    def _read_file(self, file_path: Path) -> dict[str, Any]:
        if not file_path.exists():
            return {}
        with open(file_path, mode="rb") as toml_file:
            if sys.version_info < (3, 11):
                import tomli
                return tomli.load(toml_file)
            import tomllib
            return tomllib.load(toml_file)

    def _substitute_env_vars(self, config: dict[str, Any]) -> dict[str, Any]:
        """递归替换配置中的环境变量"""
        if isinstance(config, dict):
            return {key: self._substitute_env_vars(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._substitute_env_vars(item) for item in config]
        elif isinstance(config, str):
            # 替换 ${VAR_NAME} 格式的环境变量
            def replace_env_var(match):
                var_name = match.group(1)
                return os.getenv(var_name, match.group(0))  # 如果环境变量不存在，保持原样

            return re.sub(r'\$\{([^}]+)\}', replace_env_var, config)
        else:
            return config
