"""Model utilities for chat model management."""

from functools import cache

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel

from config.globalconfig import get_or_create_settings_ins


@cache
def get_chat_model(model_name: str = "default") -> BaseChatModel:
    """Get a chat model instance by name.
    
    Args:
        model_name: Name of the model to get. Defaults to "default".
        
    Returns:
        BaseChatModel: Initialized chat model instance.
        
    Examples:
        >>> # Get default model
        >>> model = get_chat_model()
        
        >>> # Get specific model
        >>> model = get_chat_model("gpt4o")
        >>> model = get_chat_model("claude_sonnet")
    """
    config = get_or_create_settings_ins()
    models = config.models
    model_cfg = getattr(models, "default")
    if model_name in models.model_fields_set:
        model_cfg = getattr(models, model_name)
    llm = config.llms[model_cfg]

    # 分离 external_args 和 extra_body
    external_args = llm.external_args or {}
    extra_body = llm.extra_body or {}

    # 如果有 extra_body，需要将其放入 external_args 的 extra_body 字段中
    if extra_body:
        external_args = {**external_args, "extra_body": extra_body}

    return init_chat_model(
        llm.model,
        model_provider=llm.provider,
        **external_args,
    )
